//
//  GestureCoordinator.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import Combine

/// Centralized gesture handling for spatial interface
@MainActor
class GestureCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    @Published var activeGesture: GestureType?
    @Published var gestureState: GestureState = .idle
    @Published var gestureTarget: GestureTarget?
    
    // MARK: - Private Properties
    private var hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    private var lightHaptic = UIImpactFeedbackGenerator(style: .light)
    private var heavyHaptic = UIImpactFeedbackGenerator(style: .heavy)
    
    // MARK: - Gesture Types
    enum GestureType {
        case tap
        case doubleTap
        case longPress
        case drag
        case pinch
        case rotate
        case swipe(direction: SwipeDirection)
        case twoFingerTap
        case threeFingerTap
    }
    
    enum SwipeDirection {
        case up, down, left, right
    }
    
    enum GestureState {
        case idle
        case began
        case changed
        case ended
        case cancelled
    }
    
    enum GestureTarget {
        case contact(SpatialContact)
        case space(MemorySpace)
        case relationship(ContactRelationship)
        case background
    }
    
    // MARK: - Initialization
    init() {
        setupHaptics()
    }
    
    // MARK: - Setup
    private func setupHaptics() {
        hapticFeedback.prepare()
        lightHaptic.prepare()
        heavyHaptic.prepare()
    }
    
    // MARK: - Gesture Handling
    func handleGesture(
        _ gestureType: GestureType,
        target: GestureTarget,
        state: GestureState,
        value: Any? = nil
    ) {
        activeGesture = gestureType
        gestureState = state
        gestureTarget = target
        
        switch gestureType {
        case .tap:
            handleTap(target: target, state: state)
        case .doubleTap:
            handleDoubleTap(target: target, state: state)
        case .longPress:
            handleLongPress(target: target, state: state)
        case .drag:
            handleDrag(target: target, state: state, value: value)
        case .pinch:
            handlePinch(target: target, state: state, value: value)
        case .rotate:
            handleRotate(target: target, state: state, value: value)
        case .swipe(let direction):
            handleSwipe(direction: direction, target: target, state: state)
        case .twoFingerTap:
            handleTwoFingerTap(target: target, state: state)
        case .threeFingerTap:
            handleThreeFingerTap(target: target, state: state)
        }
        
        // Reset state after gesture ends
        if state == .ended || state == .cancelled {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.activeGesture = nil
                self.gestureState = .idle
                self.gestureTarget = nil
            }
        }
    }
    
    // MARK: - Individual Gesture Handlers
    private func handleTap(target: GestureTarget, state: GestureState) {
        guard state == .ended else { return }
        
        lightHaptic.impactOccurred()
        
        switch target {
        case .contact(let contact):
            NotificationCenter.default.post(
                name: .contactTapped,
                object: contact
            )
        case .space(let space):
            NotificationCenter.default.post(
                name: .spaceTapped,
                object: space
            )
        case .relationship(let relationship):
            NotificationCenter.default.post(
                name: .relationshipTapped,
                object: relationship
            )
        case .background:
            NotificationCenter.default.post(
                name: .backgroundTapped,
                object: nil
            )
        }
    }
    
    private func handleDoubleTap(target: GestureTarget, state: GestureState) {
        guard state == .ended else { return }
        
        hapticFeedback.impactOccurred()
        
        switch target {
        case .contact(let contact):
            // Quick action (call/message)
            NotificationCenter.default.post(
                name: .contactQuickAction,
                object: contact
            )
        case .space:
            // Toggle relationship view
            NotificationCenter.default.post(
                name: .toggleRelationshipView,
                object: nil
            )
        case .background:
            // Add new contact at location
            NotificationCenter.default.post(
                name: .addContactAtLocation,
                object: nil
            )
        default:
            break
        }
    }
    
    private func handleLongPress(target: GestureTarget, state: GestureState) {
        switch state {
        case .began:
            heavyHaptic.impactOccurred()
            
            switch target {
            case .contact(let contact):
                NotificationCenter.default.post(
                    name: .contactLongPressStarted,
                    object: contact
                )
            default:
                break
            }
        case .ended:
            switch target {
            case .contact(let contact):
                NotificationCenter.default.post(
                    name: .contactLongPressEnded,
                    object: contact
                )
            default:
                break
            }
        default:
            break
        }
    }
    
    private func handleDrag(target: GestureTarget, state: GestureState, value: Any?) {
        switch target {
        case .contact(let contact):
            switch state {
            case .began:
                lightHaptic.impactOccurred()
                NotificationCenter.default.post(
                    name: .contactDragStarted,
                    object: contact,
                    userInfo: ["value": value as Any]
                )
            case .changed:
                NotificationCenter.default.post(
                    name: .contactDragChanged,
                    object: contact,
                    userInfo: ["value": value as Any]
                )
            case .ended:
                lightHaptic.impactOccurred()
                NotificationCenter.default.post(
                    name: .contactDragEnded,
                    object: contact,
                    userInfo: ["value": value as Any]
                )
            default:
                break
            }
        default:
            break
        }
    }
    
    private func handlePinch(target: GestureTarget, state: GestureState, value: Any?) {
        // Pinch gestures for zooming/scaling
        switch state {
        case .began:
            lightHaptic.impactOccurred()
        case .ended:
            lightHaptic.impactOccurred()
        default:
            break
        }
        
        NotificationCenter.default.post(
            name: .pinchGesture,
            object: target,
            userInfo: ["state": state, "value": value as Any]
        )
    }
    
    private func handleRotate(target: GestureTarget, state: GestureState, value: Any?) {
        // Rotation gestures for contact orientation
        NotificationCenter.default.post(
            name: .rotateGesture,
            object: target,
            userInfo: ["state": state, "value": value as Any]
        )
    }
    
    private func handleSwipe(direction: SwipeDirection, target: GestureTarget, state: GestureState) {
        guard state == .ended else { return }
        
        hapticFeedback.impactOccurred()
        
        NotificationCenter.default.post(
            name: .swipeGesture,
            object: target,
            userInfo: ["direction": direction]
        )
    }
    
    private func handleTwoFingerTap(target: GestureTarget, state: GestureState) {
        guard state == .ended else { return }
        
        heavyHaptic.impactOccurred()
        
        // Two finger tap for adding notes/memories
        NotificationCenter.default.post(
            name: .twoFingerTap,
            object: target
        )
    }
    
    private func handleThreeFingerTap(target: GestureTarget, state: GestureState) {
        guard state == .ended else { return }
        
        heavyHaptic.impactOccurred()
        
        // Three finger tap for advanced actions
        NotificationCenter.default.post(
            name: .threeFingerTap,
            object: target
        )
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let contactTapped = Notification.Name("contactTapped")
    static let contactQuickAction = Notification.Name("contactQuickAction")
    static let contactLongPressStarted = Notification.Name("contactLongPressStarted")
    static let contactLongPressEnded = Notification.Name("contactLongPressEnded")
    static let contactDragStarted = Notification.Name("contactDragStarted")
    static let contactDragChanged = Notification.Name("contactDragChanged")
    static let contactDragEnded = Notification.Name("contactDragEnded")
    
    static let spaceTapped = Notification.Name("spaceTapped")
    static let backgroundTapped = Notification.Name("backgroundTapped")
    static let addContactAtLocation = Notification.Name("addContactAtLocation")
    static let toggleRelationshipView = Notification.Name("toggleRelationshipView")
    
    static let relationshipTapped = Notification.Name("relationshipTapped")
    
    static let pinchGesture = Notification.Name("pinchGesture")
    static let rotateGesture = Notification.Name("rotateGesture")
    static let swipeGesture = Notification.Name("swipeGesture")
    static let twoFingerTap = Notification.Name("twoFingerTap")
    static let threeFingerTap = Notification.Name("threeFingerTap")
    
    static let spatialNavigationChanged = Notification.Name("spatialNavigationChanged")
}

// MARK: - Gesture Utilities
extension GestureCoordinator {
    
    /// Create a unified gesture for contact nodes
    static func contactGesture(
        for contact: SpatialContact,
        coordinator: GestureCoordinator,
        onTap: @escaping () -> Void,
        onDragChanged: @escaping (DragGesture.Value) -> Void,
        onDragEnded: @escaping (DragGesture.Value) -> Void
    ) -> some Gesture {
        let tapGesture = TapGesture()
            .onEnded {
                coordinator.handleGesture(
                    .tap,
                    target: .contact(contact),
                    state: .ended
                )
                onTap()
            }
        
        let dragGesture = DragGesture()
            .onChanged { value in
                coordinator.handleGesture(
                    .drag,
                    target: .contact(contact),
                    state: .changed,
                    value: value
                )
                onDragChanged(value)
            }
            .onEnded { value in
                coordinator.handleGesture(
                    .drag,
                    target: .contact(contact),
                    state: .ended,
                    value: value
                )
                onDragEnded(value)
            }
        
        return dragGesture.simultaneously(with: tapGesture)
    }
}
