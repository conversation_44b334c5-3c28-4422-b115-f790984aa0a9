//
//  GestureLearningSystem.swift
//  FirmBond
//
//  AI-powered gesture learning and adaptation system
//

import SwiftUI
import Combine

@MainActor
class GestureLearningSystem: ObservableObject {
    
    // MARK: - Published Properties
    @Published var userGestureProfile = UserGestureProfile()
    @Published var suggestedGestures: [GestureSuggestion] = []
    @Published var adaptiveThresholds = AdaptiveThresholds()
    @Published var learningProgress: Double = 0.0
    
    // MARK: - Private Properties
    private var gestureHistory: [GestureEvent] = []
    private var cancellables = Set<AnyCancellable>()
    private let maxHistorySize = 1000
    
    // MARK: - Initialization
    init() {
        loadUserProfile()
        startLearningTimer()
    }
    
    // MARK: - Gesture Recording
    func recordGesture(
        type: GestureType,
        duration: TimeInterval,
        distance: CGFloat,
        velocity: CGFloat,
        accuracy: Double,
        context: GestureContext
    ) {
        let event = GestureEvent(
            type: type,
            timestamp: Date(),
            duration: duration,
            distance: distance,
            velocity: velocity,
            accuracy: accuracy,
            context: context
        )
        
        gestureHistory.append(event)
        
        // Maintain history size
        if gestureHistory.count > maxHistorySize {
            gestureHistory.removeFirst(gestureHistory.count - maxHistorySize)
        }
        
        // Update user profile
        updateUserProfile(with: event)
        
        // Generate suggestions
        generateSuggestions()
        
        // Adapt thresholds
        adaptThresholds()
    }
    
    // MARK: - User Profile Updates
    private func updateUserProfile(with event: GestureEvent) {
        // Update gesture frequency
        userGestureProfile.gestureFrequency[event.type, default: 0] += 1
        
        // Update preferred velocity
        let currentVelocity = userGestureProfile.preferredVelocity[event.type] ?? event.velocity
        userGestureProfile.preferredVelocity[event.type] = (currentVelocity + event.velocity) / 2
        
        // Update preferred distance
        let currentDistance = userGestureProfile.preferredDistance[event.type] ?? event.distance
        userGestureProfile.preferredDistance[event.type] = (currentDistance + event.distance) / 2
        
        // Update accuracy tracking
        userGestureProfile.averageAccuracy[event.type] = calculateAverageAccuracy(for: event.type)
        
        // Update context preferences
        userGestureProfile.contextPreferences[event.context, default: 0] += 1
        
        // Calculate learning progress
        updateLearningProgress()
    }
    
    // MARK: - Adaptive Thresholds
    private func adaptThresholds() {
        // Adjust swipe distance thresholds based on user patterns
        for gestureType in GestureType.allCases {
            if let preferredDistance = userGestureProfile.preferredDistance[gestureType] {
                switch gestureType {
                case .swipeUp, .swipeDown, .swipeLeft, .swipeRight:
                    adaptiveThresholds.swipeDistance = max(50, min(200, preferredDistance * 0.8))
                default:
                    break
                }
            }
        }
        
        // Adjust velocity thresholds
        let averageVelocity = userGestureProfile.preferredVelocity.values.reduce(0, +) / Double(userGestureProfile.preferredVelocity.count)
        adaptiveThresholds.velocityThreshold = max(300, min(800, averageVelocity * 0.7))
        
        // Adjust timing thresholds based on user speed
        let recentEvents = gestureHistory.suffix(50)
        let averageDuration = recentEvents.map(\.duration).reduce(0, +) / Double(recentEvents.count)
        adaptiveThresholds.longPressThreshold = max(0.3, min(1.0, averageDuration * 1.2))
    }
    
    // MARK: - Suggestion Generation
    private func generateSuggestions() {
        suggestedGestures.removeAll()
        
        // Analyze recent patterns
        let recentEvents = gestureHistory.suffix(20)
        let gestureSequences = extractSequences(from: recentEvents)
        
        // Suggest efficiency improvements
        if let inefficientPattern = findInefficientPattern() {
            suggestedGestures.append(
                GestureSuggestion(
                    type: .efficiency,
                    title: "Try a faster gesture",
                    description: "You often use \(inefficientPattern.current.description). Try \(inefficientPattern.suggested.description) instead.",
                    currentGesture: inefficientPattern.current,
                    suggestedGesture: inefficientPattern.suggested,
                    potentialTimeSaving: inefficientPattern.timeSaving
                )
            )
        }
        
        // Suggest new gesture combinations
        if let newCombination = suggestNewCombination() {
            suggestedGestures.append(
                GestureSuggestion(
                    type: .newFeature,
                    title: "Discover new gesture",
                    description: "Based on your usage, you might like \(newCombination.description)",
                    currentGesture: nil,
                    suggestedGesture: newCombination.gesture,
                    potentialTimeSaving: newCombination.benefit
                )
            )
        }
        
        // Suggest accuracy improvements
        if let accuracyImprovement = suggestAccuracyImprovement() {
            suggestedGestures.append(accuracyImprovement)
        }
    }
    
    // MARK: - Pattern Analysis
    private func findInefficientPattern() -> (current: GestureType, suggested: GestureType, timeSaving: TimeInterval)? {
        // Look for patterns where user repeatedly uses slow gestures
        let frequentGestures = userGestureProfile.gestureFrequency.sorted { $0.value > $1.value }
        
        for (gesture, frequency) in frequentGestures.prefix(3) {
            if frequency > 10 {
                // Check if there's a faster alternative
                if let fasterAlternative = findFasterAlternative(for: gesture) {
                    let currentTime = averageExecutionTime(for: gesture)
                    let suggestedTime = averageExecutionTime(for: fasterAlternative)
                    
                    if currentTime > suggestedTime + 0.2 {
                        return (gesture, fasterAlternative, currentTime - suggestedTime)
                    }
                }
            }
        }
        
        return nil
    }
    
    private func suggestNewCombination() -> (gesture: GestureType, description: String, benefit: TimeInterval)? {
        // Analyze unused gestures that might be beneficial
        let unusedGestures = GestureType.allCases.filter { gesture in
            (userGestureProfile.gestureFrequency[gesture] ?? 0) < 3
        }
        
        // Suggest based on context
        let mostUsedContext = userGestureProfile.contextPreferences.max { $0.value < $1.value }?.key
        
        if let context = mostUsedContext {
            for gesture in unusedGestures {
                if isGestureBeneficialForContext(gesture, context: context) {
                    return (
                        gesture,
                        "Perfect for \(context.description) tasks",
                        0.5
                    )
                }
            }
        }
        
        return nil
    }
    
    private func suggestAccuracyImprovement() -> GestureSuggestion? {
        // Find gestures with low accuracy
        let lowAccuracyGestures = userGestureProfile.averageAccuracy.filter { $0.value < 0.7 }
        
        if let (gesture, accuracy) = lowAccuracyGestures.first {
            return GestureSuggestion(
                type: .accuracy,
                title: "Improve gesture accuracy",
                description: "Your \(gesture.description) accuracy is \(Int(accuracy * 100))%. Try slower, more deliberate movements.",
                currentGesture: gesture,
                suggestedGesture: gesture,
                potentialTimeSaving: 0.0
            )
        }
        
        return nil
    }
    
    // MARK: - Helper Methods
    private func calculateAverageAccuracy(for gestureType: GestureType) -> Double {
        let relevantEvents = gestureHistory.filter { $0.type == gestureType }
        guard !relevantEvents.isEmpty else { return 1.0 }
        
        let totalAccuracy = relevantEvents.map(\.accuracy).reduce(0, +)
        return totalAccuracy / Double(relevantEvents.count)
    }
    
    private func averageExecutionTime(for gestureType: GestureType) -> TimeInterval {
        let relevantEvents = gestureHistory.filter { $0.type == gestureType }
        guard !relevantEvents.isEmpty else { return 1.0 }
        
        let totalTime = relevantEvents.map(\.duration).reduce(0, +)
        return totalTime / Double(relevantEvents.count)
    }
    
    private func findFasterAlternative(for gesture: GestureType) -> GestureType? {
        // Define gesture alternatives
        let alternatives: [GestureType: GestureType] = [
            .swipeUp: .multiTap,
            .circle: .pressure,
            .spiral: .swipeRight
        ]
        
        return alternatives[gesture]
    }
    
    private func isGestureBeneficialForContext(_ gesture: GestureType, context: GestureContext) -> Bool {
        // Define context-gesture benefits
        switch (gesture, context) {
        case (.circle, .browsing):
            return true
        case (.spiral, .editing):
            return true
        case (.pressure, .selection):
            return true
        default:
            return false
        }
    }
    
    private func extractSequences(from events: ArraySlice<GestureEvent>) -> [[GestureType]] {
        // Extract common gesture sequences for pattern recognition
        var sequences: [[GestureType]] = []
        
        for i in 0..<(events.count - 2) {
            let sequence = Array(events[i..<(i+3)]).map(\.type)
            sequences.append(sequence)
        }
        
        return sequences
    }
    
    private func updateLearningProgress() {
        let totalGestures = gestureHistory.count
        let uniqueGestures = Set(gestureHistory.map(\.type)).count
        let maxUniqueGestures = GestureType.allCases.count
        
        learningProgress = min(1.0, Double(uniqueGestures) / Double(maxUniqueGestures) * 0.7 + Double(totalGestures) / 100.0 * 0.3)
    }
    
    private func startLearningTimer() {
        Timer.publish(every: 60, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                self.generateSuggestions()
                self.saveUserProfile()
            }
            .store(in: &cancellables)
    }
    
    private func loadUserProfile() {
        // TODO: Load from UserDefaults or Core Data
    }
    
    private func saveUserProfile() {
        // TODO: Save to UserDefaults or Core Data
    }
}

// MARK: - Supporting Types
struct UserGestureProfile {
    var gestureFrequency: [GestureType: Int] = [:]
    var preferredVelocity: [GestureType: CGFloat] = [:]
    var preferredDistance: [GestureType: CGFloat] = [:]
    var averageAccuracy: [GestureType: Double] = [:]
    var contextPreferences: [GestureContext: Int] = [:]
    var skillLevel: SkillLevel = .beginner
}

struct GestureEvent {
    let type: GestureType
    let timestamp: Date
    let duration: TimeInterval
    let distance: CGFloat
    let velocity: CGFloat
    let accuracy: Double
    let context: GestureContext
}

struct GestureSuggestion: Identifiable {
    let id = UUID()
    let type: SuggestionType
    let title: String
    let description: String
    let currentGesture: GestureType?
    let suggestedGesture: GestureType
    let potentialTimeSaving: TimeInterval
}

enum SuggestionType {
    case efficiency
    case newFeature
    case accuracy
    case combination
}

enum GestureContext {
    case browsing
    case editing
    case selection
    case navigation
    
    var description: String {
        switch self {
        case .browsing: return "browsing"
        case .editing: return "editing"
        case .selection: return "selection"
        case .navigation: return "navigation"
        }
    }
}

enum SkillLevel {
    case beginner
    case intermediate
    case advanced
    case expert
}

struct AdaptiveThresholds {
    var swipeDistance: CGFloat = 100
    var velocityThreshold: CGFloat = 500
    var longPressThreshold: TimeInterval = 0.5
    var accuracyThreshold: Double = 0.8
}
