//
//  CardStackViewModel.swift
//  FirmBond
//
//  Manages the gesture-based card stack state and actions
//

import SwiftUI
import Combine

@MainActor
class CardStackViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var visibleCards: [CardModel] = []
    @Published var archivedCards: [CardModel] = []
    @Published var favoritedCards: [CardModel] = []
    @Published var showUndoToast = false
    @Published var showEdgeHints = true
    @Published var hasProvidedThresholdFeedback = false
    
    // MARK: - Private Properties
    private var allCards: [CardModel] = []
    private var lastAction: CardAction?
    private var undoTimer: Timer?
    
    // MARK: - Constants
    private let maxVisibleCards = 5
    private let undoToastDuration: TimeInterval = 3.0
    
    // MARK: - Initialization
    init() {
        // Hide edge hints after first use
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            withAnimation(.easeOut(duration: 1.0)) {
                self.showEdgeHints = false
            }
        }
    }
    
    // MARK: - Card Management
    func loadCards(from people: [Person]) {
        allCards = people.map { CardModel(person: $0) }
        refreshVisibleCards()
    }
    
    private func refreshVisibleCards() {
        // Show only cards that aren't archived or favorited
        let availableCards = allCards.filter { card in
            !archivedCards.contains(where: { $0.id == card.id }) &&
            !favoritedCards.contains(where: { $0.id == card.id })
        }
        
        visibleCards = Array(availableCards.prefix(maxVisibleCards))
    }
    
    // MARK: - Card Actions
    func deleteCard(_ card: CardModel) {
        performAction(.delete(card))
    }
    
    func archiveCard(_ card: CardModel) {
        performAction(.archive(card))
    }
    
    func favoriteCard(_ card: CardModel) {
        performAction(.favorite(card))
    }
    
    func deferCard(_ card: CardModel) {
        performAction(.defer(card))
    }
    
    private func performAction(_ action: CardAction) {
        lastAction = action
        
        switch action {
        case .delete(let card):
            removeCardFromVisible(card)
            // In a real app, this would delete from Core Data
            
        case .archive(let card):
            removeCardFromVisible(card)
            archivedCards.append(card)
            
        case .favorite(let card):
            removeCardFromVisible(card)
            favoritedCards.append(card)
            
        case .defer(let card):
            // Move to bottom of stack
            if let index = visibleCards.firstIndex(where: { $0.id == card.id }) {
                let deferredCard = visibleCards.remove(at: index)
                // Add to end of allCards
                if let allIndex = allCards.firstIndex(where: { $0.id == card.id }) {
                    let movedCard = allCards.remove(at: allIndex)
                    allCards.append(movedCard)
                }
            }
        }
        
        refreshVisibleCards()
        showUndoToast()
    }
    
    private func removeCardFromVisible(_ card: CardModel) {
        visibleCards.removeAll { $0.id == card.id }
    }
    
    // MARK: - Undo Functionality
    func undoLastAction() {
        guard let action = lastAction else { return }
        
        switch action {
        case .delete(let card):
            // Restore card to visible stack
            visibleCards.append(card)
            
        case .archive(let card):
            archivedCards.removeAll { $0.id == card.id }
            
        case .favorite(let card):
            favoritedCards.removeAll { $0.id == card.id }
            
        case .defer(let card):
            // Move back to top
            if let allIndex = allCards.firstIndex(where: { $0.id == card.id }) {
                let restoredCard = allCards.remove(at: allIndex)
                allCards.insert(restoredCard, at: 0)
            }
        }
        
        refreshVisibleCards()
        hideUndoToast()
        lastAction = nil
    }
    
    private func showUndoToast() {
        showUndoToast = true
        
        // Auto-hide after duration
        undoTimer?.invalidate()
        undoTimer = Timer.scheduledTimer(withTimeInterval: undoToastDuration, repeats: false) { _ in
            DispatchQueue.main.async {
                self.hideUndoToast()
            }
        }
    }
    
    private func hideUndoToast() {
        withAnimation(.easeOut(duration: 0.3)) {
            showUndoToast = false
        }
        undoTimer?.invalidate()
        lastAction = nil
    }
}

// MARK: - Supporting Types
struct CardModel: Identifiable, Equatable {
    let id = UUID()
    let person: Person
    var isVisible = true
    
    static func == (lhs: CardModel, rhs: CardModel) -> Bool {
        lhs.id == rhs.id
    }
}

enum CardAction {
    case delete(CardModel)
    case archive(CardModel)
    case favorite(CardModel)
    case `defer`(CardModel)
}

struct ActionIcon: Identifiable {
    let id = UUID()
    let type: ActionType
    var position: CGPoint
    var isHighlighted = false
    
    enum ActionType {
        case delete
        case archive
        case favorite
        
        var systemImage: String {
            switch self {
            case .delete: return "trash"
            case .archive: return "archivebox"
            case .favorite: return "heart"
            }
        }
        
        var color: Color {
            switch self {
            case .delete: return .red
            case .archive: return .orange
            case .favorite: return .pink
            }
        }
    }
}
