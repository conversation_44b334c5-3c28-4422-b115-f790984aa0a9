//
//  BlackboardViewModel.swift
//  FirmBond
//
//  Manages the relationship mind-mapping canvas state
//

import SwiftUI
import Combine

@MainActor
class BlackboardViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var contacts: [CanvasContact] = []
    @Published var connections: [ConnectionLine] = []
    @Published var canvasScale: CGFloat = 1.0
    @Published var canvasOffset = CGSize.zero
    @Published var selectedContactIds: Set<UUID> = []
    @Published var isEditingMode = false
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupAutoSave()
    }
    
    // MARK: - Contact Management
    func loadContacts(from people: [Person]) {
        // Convert Person objects to CanvasContact objects
        let newContacts = people.enumerated().map { index, person in
            // Check if we already have this contact positioned
            if let existingContact = contacts.first(where: { $0.personId == person.id }) {
                return existingContact
            } else {
                // Create new contact with smart positioning
                return CanvasContact(
                    person: person,
                    position: generateSmartPosition(for: index, total: people.count)
                )
            }
        }
        
        contacts = newContacts
    }
    
    func updateContactPosition(_ contactId: UUID, position: CGPoint) {
        if let index = contacts.firstIndex(where: { $0.id == contactId }) {
            contacts[index].position = position
            contacts[index].lastMoved = Date()
        }
    }
    
    func findContactAt(_ position: CGPoint, threshold: CGFloat = 30) -> CanvasContact? {
        return contacts.first { contact in
            let distance = sqrt(
                pow(contact.position.x - position.x, 2) +
                pow(contact.position.y - position.y, 2)
            )
            return distance <= threshold
        }
    }
    
    func findNearbyContact(to position: CGPoint, excluding excludeId: UUID, threshold: CGFloat) -> CanvasContact? {
        return contacts.first { contact in
            guard contact.id != excludeId else { return false }
            
            let distance = sqrt(
                pow(contact.position.x - position.x, 2) +
                pow(contact.position.y - position.y, 2)
            )
            return distance <= threshold
        }
    }
    
    // MARK: - Connection Management
    func createConnection(from startId: UUID, to endId: UUID, style: ConnectionStyle = .curved) {
        // Check if connection already exists
        let connectionExists = connections.contains { connection in
            (connection.startContactId == startId && connection.endContactId == endId) ||
            (connection.startContactId == endId && connection.endContactId == startId)
        }
        
        guard !connectionExists else { return }
        
        let newConnection = ConnectionLine(
            startContactId: startId,
            endContactId: endId,
            style: style,
            color: generateConnectionColor(),
            thickness: 2.0
        )
        
        connections.append(newConnection)
    }
    
    func removeConnection(_ connectionId: UUID) {
        connections.removeAll { $0.id == connectionId }
    }
    
    func updateConnectionLabel(_ connectionId: UUID, label: String) {
        if let index = connections.firstIndex(where: { $0.id == connectionId }) {
            connections[index].label = label.isEmpty ? nil : label
        }
    }
    
    func updateConnectionStyle(_ connectionId: UUID, style: ConnectionStyle) {
        if let index = connections.firstIndex(where: { $0.id == connectionId }) {
            connections[index].style = style
        }
    }
    
    func clearAllConnections() {
        withAnimation(.easeOut(duration: 0.3)) {
            connections.removeAll()
        }
    }
    
    // MARK: - Layout Management
    func resetLayout() {
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
            for (index, contact) in contacts.enumerated() {
                contacts[index].position = generateSmartPosition(for: index, total: contacts.count)
            }
            canvasScale = 1.0
            canvasOffset = .zero
        }
    }
    
    func organizeByRelationshipType() {
        // Group contacts by relationship type and arrange spatially
        let workContacts = contacts.filter { $0.person.relationship?.lowercased().contains("work") == true }
        let familyContacts = contacts.filter { $0.person.relationship?.lowercased().contains("family") == true }
        let friendContacts = contacts.filter { $0.person.relationship?.lowercased().contains("friend") == true }
        let otherContacts = contacts.filter { contact in
            !workContacts.contains(where: { $0.id == contact.id }) &&
            !familyContacts.contains(where: { $0.id == contact.id }) &&
            !friendContacts.contains(where: { $0.id == contact.id })
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
            // Position work contacts in top-left
            arrangeContactsInCluster(workContacts, center: CGPoint(x: 150, y: 150))
            
            // Position family contacts in top-right
            arrangeContactsInCluster(familyContacts, center: CGPoint(x: 350, y: 150))
            
            // Position friends in bottom-left
            arrangeContactsInCluster(friendContacts, center: CGPoint(x: 150, y: 350))
            
            // Position others in bottom-right
            arrangeContactsInCluster(otherContacts, center: CGPoint(x: 350, y: 350))
        }
    }
    
    // MARK: - Private Methods
    private func generateSmartPosition(for index: Int, total: Int) -> CGPoint {
        // Create a spiral layout for natural distribution
        let angle = Double(index) * (2 * .pi / 8) // 8 contacts per ring
        let ring = index / 8
        let radius = 120 + (Double(ring) * 80)
        
        let x = 250 + cos(angle) * radius
        let y = 300 + sin(angle) * radius
        
        return CGPoint(x: x, y: y)
    }
    
    private func arrangeContactsInCluster(_ contacts: [CanvasContact], center: CGPoint) {
        for (index, contact) in contacts.enumerated() {
            let angle = Double(index) * (2 * .pi / Double(max(contacts.count, 1)))
            let radius = 60.0
            
            let x = center.x + cos(angle) * radius
            let y = center.y + sin(angle) * radius
            
            if let contactIndex = self.contacts.firstIndex(where: { $0.id == contact.id }) {
                self.contacts[contactIndex].position = CGPoint(x: x, y: y)
            }
        }
    }
    
    private func generateConnectionColor() -> Color {
        let colors: [Color] = [
            .blue, .green, .orange, .purple, .pink, .yellow, .cyan, .mint
        ]
        return colors.randomElement() ?? .blue
    }
    
    private func setupAutoSave() {
        // Auto-save canvas state periodically
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                self.saveCanvasState()
            }
            .store(in: &cancellables)
    }
    
    private func saveCanvasState() {
        // TODO: Implement persistence to UserDefaults or Core Data
        // For now, just print debug info
        print("Canvas state: \(contacts.count) contacts, \(connections.count) connections")
    }
}

// MARK: - Supporting Models
struct CanvasContact: Identifiable, Equatable {
    let id = UUID()
    let personId: UUID
    let person: Person
    var position: CGPoint
    var scale: CGFloat = 1.0
    var rotation: Double = 0.0
    var lastMoved: Date = Date()
    var isHighlighted = false
    
    init(person: Person, position: CGPoint) {
        self.personId = person.id ?? UUID()
        self.person = person
        self.position = position
    }
    
    static func == (lhs: CanvasContact, rhs: CanvasContact) -> Bool {
        lhs.id == rhs.id
    }
}

struct ConnectionLine: Identifiable, Equatable {
    let id = UUID()
    let startContactId: UUID
    let endContactId: UUID
    var style: ConnectionStyle
    var color: Color
    var thickness: CGFloat
    var label: String?
    var strength: Double = 1.0 // 0.0 to 1.0
    var createdDate = Date()
    
    static func == (lhs: ConnectionLine, rhs: ConnectionLine) -> Bool {
        lhs.id == rhs.id
    }
}

enum ConnectionStyle: CaseIterable {
    case straight
    case curved
    case zigzag
    
    var displayName: String {
        switch self {
        case .straight: return "Direct"
        case .curved: return "Curved"
        case .zigzag: return "Complex"
        }
    }
}
