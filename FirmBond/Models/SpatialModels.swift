//
//  SpatialModels.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

// MARK: - Spatial Data Models

/// Represents a virtual space in the memory palace
struct MemorySpace: Identifiable, Codable {
    var id: UUID
    let name: String
    let icon: String
    let color: Color
    let spaceType: SpaceType
    var contacts: [SpatialContact] = []
    var relationships: [ContactRelationship] = []
    var memoryTriggers: [MemoryTrigger] = []
    var backgroundTheme: BackgroundTheme

    enum CodingKeys: String, CodingKey {
        case id, name, icon, spaceType, contacts, relationships, memoryTriggers, backgroundTheme
    }

    init(name: String, icon: String, color: Color, spaceType: SpaceType, backgroundTheme: BackgroundTheme = .default) {
        self.id = UUID()
        self.name = name
        self.icon = icon
        self.color = color
        self.spaceType = spaceType
        self.backgroundTheme = backgroundTheme
    }

    // Custom encoding/decoding for Color
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        icon = try container.decode(String.self, forKey: .icon)
        spaceType = try container.decode(SpaceType.self, forKey: .spaceType)
        contacts = try container.decode([SpatialContact].self, forKey: .contacts)
        relationships = try container.decode([ContactRelationship].self, forKey: .relationships)
        memoryTriggers = try container.decode([MemoryTrigger].self, forKey: .memoryTriggers)
        backgroundTheme = try container.decode(BackgroundTheme.self, forKey: .backgroundTheme)

        // Set color based on space type
        self.color = spaceType.defaultColor
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(icon, forKey: .icon)
        try container.encode(spaceType, forKey: .spaceType)
        try container.encode(contacts, forKey: .contacts)
        try container.encode(relationships, forKey: .relationships)
        try container.encode(memoryTriggers, forKey: .memoryTriggers)
        try container.encode(backgroundTheme, forKey: .backgroundTheme)
    }
    
    func contact(for id: UUID) -> SpatialContact? {
        return contacts.first { $0.id == id }
    }
}

/// Types of memory spaces
enum SpaceType: String, CaseIterable, Codable {
    case home = "home"
    case work = "work"
    case social = "social"
    case family = "family"
    case places = "places"
    case hobbies = "hobbies"
    
    var displayName: String {
        switch self {
        case .home: return "Home"
        case .work: return "Work"
        case .social: return "Social"
        case .family: return "Family"
        case .places: return "Places"
        case .hobbies: return "Hobbies"
        }
    }
    
    var icon: String {
        switch self {
        case .home: return "house.fill"
        case .work: return "building.2.fill"
        case .social: return "person.3.fill"
        case .family: return "heart.fill"
        case .places: return "location.fill"
        case .hobbies: return "gamecontroller.fill"
        }
    }
    
    var defaultColor: Color {
        switch self {
        case .home: return DesignSystem.Colors.mutedGold
        case .work: return DesignSystem.Colors.sageGreen
        case .social: return DesignSystem.Colors.softBlue
        case .family: return .red.opacity(0.7)
        case .places: return .purple.opacity(0.7)
        case .hobbies: return .orange.opacity(0.7)
        }
    }
}

/// Contact positioned in spatial context
struct SpatialContact: Identifiable, Codable {
    var id: UUID
    let personId: UUID
    var position: CGPoint
    var spaceId: UUID
    var scale: CGFloat = 1.0
    var rotation: Double = 0.0
    var lastInteractionDate: Date?

    // Computed property to get the actual Person object
    var person: Person? {
        // This will be resolved by the view model
        return nil
    }

    init(personId: UUID, position: CGPoint, spaceId: UUID) {
        self.id = UUID()
        self.personId = personId
        self.position = position
        self.spaceId = spaceId
        self.lastInteractionDate = Date()
    }
}

/// Relationship between contacts
struct ContactRelationship: Identifiable, Codable {
    var id: UUID
    let fromContactId: UUID
    let toContactId: UUID
    var type: RelationshipType
    var strength: Double // 0.0 to 1.0
    let createdDate: Date
    var lastUpdated: Date
    
    var color: Color {
        switch type {
        case .family: return .red.opacity(0.8)
        case .friend: return .blue.opacity(0.8)
        case .colleague: return .green.opacity(0.8)
        case .romantic: return .pink.opacity(0.8)
        case .acquaintance: return .gray.opacity(0.6)
        case .mentor: return .purple.opacity(0.8)
        case .neighbor: return .orange.opacity(0.8)
        }
    }
    
    init(fromContactId: UUID, toContactId: UUID, type: RelationshipType, strength: Double = 0.5) {
        self.id = UUID()
        self.fromContactId = fromContactId
        self.toContactId = toContactId
        self.type = type
        self.strength = max(0.0, min(1.0, strength))
        self.createdDate = Date()
        self.lastUpdated = Date()
    }
}

/// Types of relationships
enum RelationshipType: String, CaseIterable, Codable {
    case family = "family"
    case friend = "friend"
    case colleague = "colleague"
    case romantic = "romantic"
    case acquaintance = "acquaintance"
    case mentor = "mentor"
    case neighbor = "neighbor"
    
    var displayName: String {
        return rawValue.capitalized
    }
}

/// Memory triggers for enhanced recall
struct MemoryTrigger: Identifiable, Codable {
    var id: UUID
    let type: TriggerType
    let content: String
    let position: CGPoint
    let associatedContactIds: [UUID]
    let createdDate: Date
    var isActive: Bool = true
    
    enum TriggerType: String, CaseIterable, Codable {
        case photo = "photo"
        case music = "music"
        case location = "location"
        case object = "object"
        case scent = "scent"
        case quote = "quote"
        case memory = "memory"
        
        var icon: String {
            switch self {
            case .photo: return "photo"
            case .music: return "music.note"
            case .location: return "location"
            case .object: return "cube.box"
            case .scent: return "nose"
            case .quote: return "quote.bubble"
            case .memory: return "brain.head.profile"
            }
        }
    }
    
    init(type: TriggerType, content: String, position: CGPoint, associatedContactIds: [UUID] = []) {
        self.id = UUID()
        self.type = type
        self.content = content
        self.position = position
        self.associatedContactIds = associatedContactIds
        self.createdDate = Date()
    }
}

/// Background themes for spaces
enum BackgroundTheme: String, CaseIterable, Codable {
    case `default` = "default"
    case cozy = "cozy"
    case modern = "modern"
    case nature = "nature"
    case minimal = "minimal"
    case vintage = "vintage"
    
    var displayName: String {
        return rawValue.capitalized
    }
}

// MARK: - Spatial Placement Suggestion
struct SpacePlacementSuggestion {
    let spaceType: SpaceType
    let position: CGPoint
    let confidence: Double // 0.0 to 1.0
    let reason: String
    
    init(spaceType: SpaceType, position: CGPoint, confidence: Double, reason: String = "") {
        self.spaceType = spaceType
        self.position = position
        self.confidence = max(0.0, min(1.0, confidence))
        self.reason = reason
    }
}

// MARK: - Default Spaces Factory
struct DefaultSpacesFactory {
    static func createDefaultSpaces() -> [MemorySpace] {
        return [
            MemorySpace(
                name: "Home",
                icon: "house.fill",
                color: DesignSystem.Colors.mutedGold,
                spaceType: .home,
                backgroundTheme: .cozy
            ),
            MemorySpace(
                name: "Work",
                icon: "building.2.fill",
                color: DesignSystem.Colors.sageGreen,
                spaceType: .work,
                backgroundTheme: .modern
            ),
            MemorySpace(
                name: "Social",
                icon: "person.3.fill",
                color: DesignSystem.Colors.softBlue,
                spaceType: .social,
                backgroundTheme: .default
            ),
            MemorySpace(
                name: "Family",
                icon: "heart.fill",
                color: .red.opacity(0.7),
                spaceType: .family,
                backgroundTheme: .cozy
            ),
            MemorySpace(
                name: "Places",
                icon: "location.fill",
                color: .purple.opacity(0.7),
                spaceType: .places,
                backgroundTheme: .nature
            )
        ]
    }
}
