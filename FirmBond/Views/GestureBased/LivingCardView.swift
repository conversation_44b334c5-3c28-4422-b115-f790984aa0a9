//
//  LivingCardView.swift
//  FirmBond
//
//  Individual card in the gesture-based stack
//

import SwiftUI

struct LivingCardView: View {
    let person: Person
    let stackIndex: Int
    let totalCards: Int
    let isTopCard: Bool
    
    @State private var isPressed = false
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg,
            shadowStyle: shadowStyle
        ) {
            cardContent
        }
        .frame(width: 320, height: 200)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .overlay(
            gestureHints
        )
        .overlay(
            cardBorder
        )
    }
    
    // MARK: - Card Content
    private var cardContent: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Header with avatar and name
            HStack(spacing: DesignSystem.Spacing.md) {
                avatarView
                
                VStack(alignment: .leading, spacing: 4) {
                    nameView
                    titleView
                }
                
                Spacer()
                
                if isTopCard {
                    gestureIndicator
                }
            }
            
            Spacer()
            
            // Recent memory snippet
            recentMemoryView
        }
    }
    
    // MARK: - Avatar View
    private var avatarView: some View {
        ZStack {
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            DesignSystem.Colors.mutedGold,
                            DesignSystem.Colors.mutedGold.opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 60, height: 60)
            
            Text(person.initials)
                .font(DesignSystem.Typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
        .shadow(
            color: DesignSystem.Colors.mutedGold.opacity(0.3),
            radius: 8,
            x: 0,
            y: 4
        )
    }
    
    // MARK: - Name View
    private var nameView: some View {
        Text(person.fullName)
            .font(DesignSystem.Typography.title3)
            .fontWeight(.semibold)
            .foregroundColor(DesignSystem.Colors.warmBlack)
            .lineLimit(1)
    }
    
    // MARK: - Title View
    private var titleView: some View {
        Group {
            if let title = person.title, !title.isEmpty {
                Text(title)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .lineLimit(1)
            } else {
                Text("No title")
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.6))
                    .italic()
            }
        }
    }
    
    // MARK: - Gesture Indicator
    private var gestureIndicator: some View {
        VStack(spacing: 2) {
            Image(systemName: "arrow.up")
                .font(.caption2)
                .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.6))
            
            Text("swipe")
                .font(.caption2)
                .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.6))
        }
        .opacity(isTopCard ? 1 : 0)
        .animation(.easeInOut(duration: 0.3), value: isTopCard)
    }
    
    // MARK: - Recent Memory View
    private var recentMemoryView: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: "quote.opening")
                    .font(.caption)
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                
                Text("Recent Memory")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.subtleText)
                
                Spacer()
                
                if let lastContact = person.lastContactDate {
                    Text(lastContact, style: .relative)
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.8))
                }
            }
            
            Text(recentMemoryText)
                .font(DesignSystem.Typography.footnote)
                .foregroundColor(DesignSystem.Colors.warmBlack.opacity(0.8))
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(DesignSystem.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                .fill(DesignSystem.Colors.primaryCream.opacity(0.5))
        )
    }
    
    // MARK: - Gesture Hints
    private var gestureHints: some View {
        Group {
            if isTopCard {
                VStack {
                    // Top hint - swipe up for details
                    HStack {
                        Spacer()
                        Image(systemName: "arrow.up.circle")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.mutedGold.opacity(0.6))
                        Spacer()
                    }
                    .padding(.top, 8)
                    
                    Spacer()
                    
                    // Bottom hints - swipe directions
                    HStack {
                        Image(systemName: "arrow.left.circle")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.4))
                        
                        Spacer()
                        
                        Image(systemName: "arrow.down.circle")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.4))
                        
                        Spacer()
                        
                        Image(systemName: "arrow.right.circle")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText.opacity(0.4))
                    }
                    .padding(.bottom, 8)
                }
            }
        }
    }
    
    // MARK: - Card Border
    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
            .stroke(
                isTopCard ? DesignSystem.Colors.mutedGold.opacity(0.3) : Color.clear,
                lineWidth: 1
            )
    }
    
    // MARK: - Computed Properties
    private var shadowStyle: GlassmorphicCard.ShadowStyle {
        if isTopCard {
            return .large
        } else {
            return .soft
        }
    }
    
    private var recentMemoryText: String {
        // In a real app, this would fetch the most recent note/memory
        // For now, return a placeholder based on the person's data
        if let notes = person.notes, !notes.isEmpty {
            return String(notes.prefix(80)) + (notes.count > 80 ? "..." : "")
        } else {
            return "No recent memories recorded. Swipe up to add your first note about \(person.firstName ?? "this person")."
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        LivingCardView(
            person: Person.preview,
            stackIndex: 0,
            totalCards: 3,
            isTopCard: true
        )
    }
}
