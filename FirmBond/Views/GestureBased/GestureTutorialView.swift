//
//  GestureTutorialView.swift
//  FirmBond
//
//  Interactive gesture tutorial for first-time users
//

import SwiftUI

struct GestureTutorialView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    @StateObject private var tutorialViewModel = GestureTutorialViewModel()
    let onComplete: () -> Void
    
    @State private var currentStep = 0
    @State private var showingCelebration = false
    @State private var gestureCompleted = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dark tutorial background
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.95),
                        Color.black.opacity(0.9)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Tutorial content
                VStack(spacing: 0) {
                    // Progress indicator
                    tutorialProgress
                    
                    // Main tutorial area
                    tutorialContent(geometry: geometry)
                    
                    // Instructions
                    instructionsPanel
                }
                
                // Celebration overlay
                if showingCelebration {
                    celebrationOverlay
                }
                
                // Ghost hands animation
                ghostHandsOverlay(geometry: geometry)
            }
        }
        .onAppear {
            tutorialViewModel.startTutorial()
        }
    }
    
    // MARK: - Tutorial Progress
    private var tutorialProgress: some View {
        VStack(spacing: 16) {
            // Progress bar
            HStack {
                ForEach(0..<tutorialViewModel.totalSteps, id: \.self) { index in
                    Rectangle()
                        .fill(index <= currentStep ? DesignSystem.Colors.mutedGold : Color.white.opacity(0.3))
                        .frame(height: 4)
                        .animation(.easeInOut(duration: 0.3), value: currentStep)
                }
            }
            .frame(height: 4)
            .padding(.horizontal, 40)
            
            // Step indicator
            Text("Step \(currentStep + 1) of \(tutorialViewModel.totalSteps)")
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.top, 60)
    }
    
    // MARK: - Tutorial Content
    private func tutorialContent(geometry: GeometryProxy) -> some View {
        ZStack {
            switch currentStep {
            case 0:
                basicSwipeTutorial(geometry: geometry)
            case 1:
                actionModeTutorial(geometry: geometry)
            case 2:
                edgeNavigationTutorial(geometry: geometry)
            case 3:
                blackboardTutorial(geometry: geometry)
            case 4:
                advancedGesturesTutorial(geometry: geometry)
            default:
                completionView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Basic Swipe Tutorial
    private func basicSwipeTutorial(geometry: GeometryProxy) -> some View {
        VStack(spacing: 40) {
            // Demo card
            LivingCardView(
                person: Person.preview,
                stackIndex: 0,
                totalCards: 1,
                isTopCard: true
            )
            .position(x: geometry.size.width / 2, y: geometry.size.height / 2 - 50)
            .gesture(
                DragGesture()
                    .onEnded { value in
                        if abs(value.translation.x) > 50 || abs(value.translation.y) > 50 {
                            completeCurrentStep()
                        }
                    }
            )
            
            // Directional hints
            VStack(spacing: 8) {
                HStack(spacing: 40) {
                    gestureHint(direction: "↑", label: "Details", color: .blue)
                    gestureHint(direction: "↓", label: "Defer", color: .gray)
                }
                HStack(spacing: 40) {
                    gestureHint(direction: "←", label: "Archive", color: .orange)
                    gestureHint(direction: "→", label: "Favorite", color: .pink)
                }
            }
            .offset(y: 100)
        }
    }
    
    // MARK: - Action Mode Tutorial
    private func actionModeTutorial(geometry: GeometryProxy) -> some View {
        VStack(spacing: 40) {
            // Demo card with action icons
            ZStack {
                LivingCardView(
                    person: Person.preview,
                    stackIndex: 0,
                    totalCards: 1,
                    isTopCard: true
                )
                
                // Simulated action icons
                if gestureCompleted {
                    HStack(spacing: 20) {
                        actionIcon(icon: "trash", color: .red)
                        actionIcon(icon: "archivebox", color: .orange)
                        actionIcon(icon: "heart", color: .pink)
                    }
                    .offset(y: -80)
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .position(x: geometry.size.width / 2, y: geometry.size.height / 2 - 50)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if value.translation.y < -100 && !gestureCompleted {
                            withAnimation(.spring()) {
                                gestureCompleted = true
                            }
                        }
                    }
                    .onEnded { value in
                        if gestureCompleted {
                            completeCurrentStep()
                        }
                    }
            )
        }
    }
    
    // MARK: - Edge Navigation Tutorial
    private func edgeNavigationTutorial(geometry: GeometryProxy) -> some View {
        ZStack {
            // Edge indicators
            VStack {
                HStack {
                    edgeIndicator(position: .leading, label: "Map", color: .green)
                    Spacer()
                    edgeIndicator(position: .trailing, label: "Groups", color: .blue)
                }
                Spacer()
                edgeIndicator(position: .bottom, label: "Search", color: .purple)
            }
            .padding(20)
            
            // Center instruction
            Text("Swipe from any edge")
                .font(.title2)
                .foregroundColor(.white)
                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    let startX = value.startLocation.x
                    let startY = value.startLocation.y
                    
                    if startX < 50 || startX > geometry.size.width - 50 || startY > geometry.size.height - 50 {
                        completeCurrentStep()
                    }
                }
        )
    }
    
    // MARK: - Blackboard Tutorial
    private func blackboardTutorial(geometry: GeometryProxy) -> some View {
        VStack(spacing: 40) {
            Text("Two-finger swipe up from bottom")
                .font(.title2)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            // Two finger animation
            HStack(spacing: 20) {
                Circle()
                    .fill(Color.white.opacity(0.8))
                    .frame(width: 20, height: 20)
                Circle()
                    .fill(Color.white.opacity(0.8))
                    .frame(width: 20, height: 20)
            }
            .offset(y: 100)
            
            Text("Opens relationship canvas")
                .font(.callout)
                .foregroundColor(.white.opacity(0.8))
        }
        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
        .onTapGesture {
            // Simulate completion for demo
            completeCurrentStep()
        }
    }
    
    // MARK: - Advanced Gestures Tutorial
    private func advancedGesturesTutorial(geometry: GeometryProxy) -> some View {
        VStack(spacing: 30) {
            Text("Advanced Gestures")
                .font(.title)
                .foregroundColor(.white)
            
            VStack(spacing: 16) {
                advancedGestureItem(gesture: "Circle", description: "Group contacts", icon: "circle")
                advancedGestureItem(gesture: "Spiral", description: "Navigate time", icon: "tornado")
                advancedGestureItem(gesture: "Triple Tap", description: "Personal hub", icon: "hand.tap")
            }
            
            Button("I'm Ready!") {
                completeCurrentStep()
            }
            .font(.headline)
            .foregroundColor(.black)
            .padding(.horizontal, 30)
            .padding(.vertical, 12)
            .background(DesignSystem.Colors.mutedGold)
            .cornerRadius(25)
        }
        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
    }
    
    // MARK: - Completion View
    private var completionView: some View {
        VStack(spacing: 30) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(DesignSystem.Colors.mutedGold)
            
            Text("Tutorial Complete!")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("You're ready to experience FirmBond's revolutionary gesture interface")
                .font(.callout)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button("Start Using FirmBond") {
                onComplete()
            }
            .font(.headline)
            .foregroundColor(.black)
            .padding(.horizontal, 30)
            .padding(.vertical, 15)
            .background(DesignSystem.Colors.mutedGold)
            .cornerRadius(25)
        }
    }
    
    // MARK: - Instructions Panel
    private var instructionsPanel: some View {
        VStack(spacing: 12) {
            Text(tutorialViewModel.currentInstruction)
                .font(.headline)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text(tutorialViewModel.currentDescription)
                .font(.callout)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.6))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 40)
    }
    
    // MARK: - Helper Views
    private func gestureHint(direction: String, label: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Text(direction)
                .font(.title)
                .foregroundColor(color)
            Text(label)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    private func actionIcon(icon: String, color: Color) -> some View {
        Circle()
            .fill(color)
            .frame(width: 50, height: 50)
            .overlay(
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
            )
    }
    
    private func edgeIndicator(position: Edge, label: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Rectangle()
                .fill(color.opacity(0.6))
                .frame(width: position == .bottom ? 100 : 4, height: position == .bottom ? 4 : 60)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    private func advancedGestureItem(gesture: String, description: String, icon: String) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(DesignSystem.Colors.mutedGold)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(gesture)
                    .font(.callout)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Celebration Overlay
    private var celebrationOverlay: some View {
        ZStack {
            Color.clear
            
            // Particle effects would go here
            VStack {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                    .scaleEffect(showingCelebration ? 1.2 : 0.8)
                
                Text("Great!")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
        }
        .transition(.scale.combined(with: .opacity))
    }
    
    // MARK: - Ghost Hands Overlay
    private func ghostHandsOverlay(geometry: GeometryProxy) -> some View {
        // Animated ghost hands showing the gesture
        EmptyView() // Placeholder for ghost hand animations
    }
    
    // MARK: - Actions
    private func completeCurrentStep() {
        withAnimation(.spring()) {
            showingCelebration = true
        }
        
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.spring()) {
                showingCelebration = false
                gestureCompleted = false
                
                if currentStep < tutorialViewModel.totalSteps - 1 {
                    currentStep += 1
                    tutorialViewModel.moveToNextStep()
                } else {
                    onComplete()
                }
            }
        }
    }
}

// MARK: - Tutorial View Model
class GestureTutorialViewModel: ObservableObject {
    @Published var currentStep = 0
    @Published var totalSteps = 5
    @Published var currentInstruction = ""
    @Published var currentDescription = ""
    
    private let instructions = [
        ("Try swiping the card", "Swipe in any direction to see what happens"),
        ("Drag up to reveal actions", "Pull the card upward to see action icons"),
        ("Swipe from screen edges", "Each edge reveals different features"),
        ("Access relationship canvas", "Two-finger swipe for mind mapping"),
        ("Master advanced gestures", "Unlock the full power of gesture control")
    ]
    
    func startTutorial() {
        updateInstructions()
    }
    
    func moveToNextStep() {
        currentStep += 1
        updateInstructions()
    }
    
    private func updateInstructions() {
        if currentStep < instructions.count {
            currentInstruction = instructions[currentStep].0
            currentDescription = instructions[currentStep].1
        }
    }
}

#Preview {
    GestureTutorialView(
        contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext)
    ) {
        // Completion action
    }
}
