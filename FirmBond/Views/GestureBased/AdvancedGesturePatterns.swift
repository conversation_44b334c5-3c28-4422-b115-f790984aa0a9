//
//  AdvancedGesturePatterns.swift
//  FirmBond
//
//  Advanced gesture patterns for power users
//

import SwiftUI

struct AdvancedGesturePatterns: ViewModifier {
    @ObservedObject var contactsViewModel: ContactsViewModel
    @State private var gestureSequence: [GestureType] = []
    @State private var lastGestureTime = Date()
    @State private var isInSequenceMode = false
    
    // Haptic feedback
    private let lightHaptic = UIImpactFeedbackGenerator(style: .light)
    private let mediumHaptic = UIImpactFeedbackGenerator(style: .medium)
    private let heavyHaptic = UIImpactFeedbackGenerator(style: .heavy)
    
    func body(content: Content) -> some View {
        content
            .gesture(
                advancedGestureRecognizer
            )
            .overlay(
                gestureSequenceIndicator
            )
    }
    
    // MARK: - Advanced Gesture Recognizer
    private var advancedGestureRecognizer: some Gesture {
        SimultaneousGesture(
            SimultaneousGesture(
                circleGesture,
                spiralGesture
            ),
            SimultaneousGesture(
                pressureGesture,
                multiFingerTapGesture
            )
        )
    }
    
    // MARK: - Circle Gesture (Group Selection)
    private var circleGesture: some Gesture {
        DragGesture(minimumDistance: 100)
            .onChanged { value in
                // Detect circular motion
                if isCircularMotion(value) {
                    addToGestureSequence(.circle)
                }
            }
            .onEnded { value in
                if isCircularMotion(value) {
                    executeCircleGesture()
                }
            }
    }
    
    // MARK: - Spiral Gesture (Time Navigation)
    private var spiralGesture: some Gesture {
        DragGesture(minimumDistance: 150)
            .onChanged { value in
                if isSpiralMotion(value) {
                    addToGestureSequence(.spiral)
                }
            }
            .onEnded { value in
                if isSpiralMotion(value) {
                    executeSpiralGesture(clockwise: isSpiralClockwise(value))
                }
            }
    }
    
    // MARK: - Pressure Gesture (Intensity Control)
    private var pressureGesture: some Gesture {
        LongPressGesture(minimumDuration: 0.1)
            .onChanged { pressing in
                if pressing {
                    // Simulate pressure sensitivity
                    addToGestureSequence(.pressure)
                }
            }
    }
    
    // MARK: - Multi-Finger Tap Gesture
    private var multiFingerTapGesture: some Gesture {
        TapGesture()
            .onEnded {
                // This would be enhanced with actual multi-finger detection
                addToGestureSequence(.multiTap)
                executeMultiFingerTap()
            }
    }
    
    // MARK: - Gesture Sequence Indicator
    private var gestureSequenceIndicator: some View {
        VStack {
            if isInSequenceMode && !gestureSequence.isEmpty {
                HStack {
                    ForEach(Array(gestureSequence.enumerated()), id: \.offset) { index, gesture in
                        gestureIcon(for: gesture)
                            .transition(.scale.combined(with: .opacity))
                    }
                }
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.7))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .padding(.top, 50)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
            Spacer()
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: gestureSequence)
    }
    
    // MARK: - Gesture Execution
    private func executeCircleGesture() {
        // Group nearby contacts
        mediumHaptic.impactOccurred()
        
        // In a real implementation, this would:
        // 1. Find contacts within the circle area
        // 2. Create a visual group
        // 3. Allow batch operations on the group
        
        print("Circle gesture executed - grouping contacts")
    }
    
    private func executeSpiralGesture(clockwise: Bool) {
        heavyHaptic.impactOccurred()
        
        if clockwise {
            // Navigate forward in time
            print("Spiral clockwise - forward in time")
        } else {
            // Navigate backward in time
            print("Spiral counter-clockwise - backward in time")
        }
    }
    
    private func executeMultiFingerTap() {
        lightHaptic.impactOccurred()
        
        // Quick actions menu
        print("Multi-finger tap - quick actions")
    }
    
    // MARK: - Gesture Recognition Helpers
    private func isCircularMotion(_ value: DragGesture.Value) -> Bool {
        // Simplified circle detection
        let distance = sqrt(pow(value.translation.x, 2) + pow(value.translation.y, 2))
        let startDistance = sqrt(pow(value.startLocation.x - value.location.x, 2) + pow(value.startLocation.y - value.location.y, 2))
        
        // Check if we've made a roughly circular path
        return distance > 100 && startDistance < 50
    }
    
    private func isSpiralMotion(_ value: DragGesture.Value) -> Bool {
        // Simplified spiral detection
        let distance = sqrt(pow(value.translation.x, 2) + pow(value.translation.y, 2))
        return distance > 150 && abs(value.translation.x) > 50 && abs(value.translation.y) > 50
    }
    
    private func isSpiralClockwise(_ value: DragGesture.Value) -> Bool {
        // Determine spiral direction based on translation pattern
        return value.translation.x > 0 && value.translation.y > 0
    }
    
    private func addToGestureSequence(_ gesture: GestureType) {
        let now = Date()
        
        // Reset sequence if too much time has passed
        if now.timeIntervalSince(lastGestureTime) > 2.0 {
            gestureSequence.removeAll()
            isInSequenceMode = false
        }
        
        // Add gesture to sequence
        if !gestureSequence.contains(gesture) {
            gestureSequence.append(gesture)
            lastGestureTime = now
            isInSequenceMode = true
            
            // Auto-clear sequence after delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                if Date().timeIntervalSince(lastGestureTime) >= 2.0 {
                    withAnimation(.easeOut(duration: 0.5)) {
                        gestureSequence.removeAll()
                        isInSequenceMode = false
                    }
                }
            }
        }
    }
    
    private func gestureIcon(for gesture: GestureType) -> some View {
        Image(systemName: gesture.iconName)
            .font(.caption)
            .foregroundColor(.white)
            .frame(width: 20, height: 20)
    }
}

// MARK: - Supporting Types
enum GestureType: CaseIterable {
    case circle
    case spiral
    case pressure
    case multiTap
    case swipeUp
    case swipeDown
    case swipeLeft
    case swipeRight
    
    var iconName: String {
        switch self {
        case .circle: return "circle"
        case .spiral: return "tornado"
        case .pressure: return "hand.point.down"
        case .multiTap: return "hand.tap"
        case .swipeUp: return "arrow.up"
        case .swipeDown: return "arrow.down"
        case .swipeLeft: return "arrow.left"
        case .swipeRight: return "arrow.right"
        }
    }
    
    var description: String {
        switch self {
        case .circle: return "Circle to group"
        case .spiral: return "Spiral for time"
        case .pressure: return "Pressure for intensity"
        case .multiTap: return "Multi-tap for actions"
        case .swipeUp: return "Swipe up"
        case .swipeDown: return "Swipe down"
        case .swipeLeft: return "Swipe left"
        case .swipeRight: return "Swipe right"
        }
    }
}

// MARK: - Gesture Combinations
struct GestureCombination {
    let sequence: [GestureType]
    let action: String
    let description: String
    
    static let predefined: [GestureCombination] = [
        GestureCombination(
            sequence: [.circle, .pressure],
            action: "groupAndFavorite",
            description: "Circle + Pressure: Group and favorite all"
        ),
        GestureCombination(
            sequence: [.spiral, .multiTap],
            action: "timelineJump",
            description: "Spiral + Multi-tap: Jump to specific time"
        ),
        GestureCombination(
            sequence: [.swipeUp, .swipeDown, .swipeUp],
            action: "quickEdit",
            description: "Up-Down-Up: Quick edit mode"
        )
    ]
}

// MARK: - View Extension
extension View {
    func advancedGesturePatterns(contactsViewModel: ContactsViewModel) -> some View {
        self.modifier(AdvancedGesturePatterns(contactsViewModel: contactsViewModel))
    }
}
