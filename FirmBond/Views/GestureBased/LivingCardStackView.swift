//
//  LivingCardStackView.swift
//  FirmBond
//
//  Revolutionary gesture-based card stack interface
//  No taps - only swipes and drags
//

import SwiftUI

struct LivingCardStackView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    @StateObject private var stackViewModel = CardStackViewModel()
    @State private var selectedContact: Person?
    @State private var showingDossier = false
    
    // Gesture state tracking
    @State private var dragOffset = CGSize.zero
    @State private var dragRotation: Double = 0
    @State private var isActionMode = false
    @State private var actionIcons: [ActionIcon] = []
    @State private var fingerPosition = CGPoint.zero
    
    // Haptic feedback
    private let lightHaptic = UIImpactFeedbackGenerator(style: .light)
    private let mediumHaptic = UIImpactFeedbackGenerator(style: .medium)
    private let heavyHaptic = UIImpactFeedbackGenerator(style: .heavy)
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background with subtle gradient
                backgroundLayer
                
                // Edge gesture indicators
                edgeIndicators(geometry: geometry)
                
                // Card stack
                cardStack(geometry: geometry)
                
                // Action icons overlay (appears during gestures)
                actionIconsOverlay
                
                // Undo toast
                undoToast
            }
        }
        .onAppear {
            stackViewModel.loadCards(from: contactsViewModel.people)
        }
        .onChange(of: contactsViewModel.people) { newPeople in
            stackViewModel.loadCards(from: newPeople)
        }
        .sheet(isPresented: $showingDossier) {
            if let contact = selectedContact {
                ContactDossierView(person: contact, viewModel: contactsViewModel)
            }
        }
    }
    
    // MARK: - Background Layer
    private var backgroundLayer: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Edge Indicators
    private func edgeIndicators(geometry: GeometryProxy) -> some View {
        ZStack {
            // Left edge - Map
            Rectangle()
                .fill(Color.green.opacity(0.1))
                .frame(width: 2)
                .position(x: 0, y: geometry.size.height / 2)
                .opacity(stackViewModel.showEdgeHints ? 1 : 0)
            
            // Right edge - Groups
            Rectangle()
                .fill(Color.blue.opacity(0.1))
                .frame(width: 2)
                .position(x: geometry.size.width, y: geometry.size.height / 2)
                .opacity(stackViewModel.showEdgeHints ? 1 : 0)
            
            // Bottom edge - Search
            Rectangle()
                .fill(Color.purple.opacity(0.1))
                .frame(height: 2)
                .position(x: geometry.size.width / 2, y: geometry.size.height)
                .opacity(stackViewModel.showEdgeHints ? 1 : 0)
        }
        .animation(.easeInOut(duration: 0.3), value: stackViewModel.showEdgeHints)
    }
    
    // MARK: - Card Stack
    private func cardStack(geometry: GeometryProxy) -> some View {
        ZStack {
            ForEach(Array(stackViewModel.visibleCards.enumerated().reversed()), id: \.element.id) { index, card in
                LivingCardView(
                    person: card.person,
                    stackIndex: index,
                    totalCards: stackViewModel.visibleCards.count,
                    isTopCard: index == stackViewModel.visibleCards.count - 1
                )
                .offset(
                    x: index == stackViewModel.visibleCards.count - 1 ? dragOffset.width : 0,
                    y: CGFloat(index) * -8 + (index == stackViewModel.visibleCards.count - 1 ? dragOffset.height : 0)
                )
                .rotationEffect(
                    .degrees(index == stackViewModel.visibleCards.count - 1 ? dragRotation : 0)
                )
                .scaleEffect(
                    index == stackViewModel.visibleCards.count - 1 ? 1.0 : 1.0 - CGFloat(stackViewModel.visibleCards.count - 1 - index) * 0.05
                )
                .zIndex(Double(index))
                .gesture(
                    index == stackViewModel.visibleCards.count - 1 ? topCardGesture(geometry: geometry) : nil
                )
            }
        }
        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
    }
    
    // MARK: - Action Icons Overlay
    private var actionIconsOverlay: some View {
        ZStack {
            ForEach(actionIcons, id: \.id) { icon in
                ActionIconView(icon: icon, fingerPosition: fingerPosition)
            }
        }
        .opacity(isActionMode ? 1 : 0)
        .animation(.easeInOut(duration: 0.2), value: isActionMode)
    }
    
    // MARK: - Undo Toast
    private var undoToast: some View {
        VStack {
            if stackViewModel.showUndoToast {
                UndoToastView {
                    stackViewModel.undoLastAction()
                }
                .transition(.move(edge: .top).combined(with: .opacity))
            }
            Spacer()
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: stackViewModel.showUndoToast)
    }
}

// MARK: - Gesture Handling Extension
extension LivingCardStackView {
    
    private func topCardGesture(geometry: GeometryProxy) -> some Gesture {
        DragGesture()
            .onChanged { value in
                handleDragChanged(value, geometry: geometry)
            }
            .onEnded { value in
                handleDragEnded(value, geometry: geometry)
            }
    }
    
    private func handleDragChanged(_ value: DragGesture.Value, geometry: GeometryProxy) {
        dragOffset = value.translation
        fingerPosition = value.location
        
        // Calculate rotation based on horizontal drag
        dragRotation = Double(value.translation.x / 20)
        
        // Determine if we're in action mode (dragged up significantly)
        if value.translation.y < -100 && !isActionMode {
            enterActionMode()
        } else if value.translation.y > -50 && isActionMode {
            exitActionMode()
        }
        
        // Update action icons if in action mode
        if isActionMode {
            updateActionIcons(for: value.translation)
        }
        
        // Provide haptic feedback at thresholds
        let distance = sqrt(pow(value.translation.x, 2) + pow(value.translation.y, 2))
        if distance > 100 && !stackViewModel.hasProvidedThresholdFeedback {
            lightHaptic.impactOccurred()
            stackViewModel.hasProvidedThresholdFeedback = true
        }
    }
    
    private func handleDragEnded(_ value: DragGesture.Value, geometry: GeometryProxy) {
        let translation = value.translation
        let velocity = value.velocity
        
        // Determine action based on final position and velocity
        if isActionMode {
            handleActionModeEnd(translation: translation, velocity: velocity)
        } else {
            handleCardSwipe(translation: translation, velocity: velocity, geometry: geometry)
        }
        
        // Reset state
        resetGestureState()
    }
    
    private func enterActionMode() {
        isActionMode = true
        mediumHaptic.impactOccurred()
        
        // Create action icons
        actionIcons = [
            ActionIcon(type: .delete, position: CGPoint(x: -60, y: -40)),
            ActionIcon(type: .archive, position: CGPoint(x: 60, y: -40)),
            ActionIcon(type: .favorite, position: CGPoint(x: 0, y: -80))
        ]
    }
    
    private func exitActionMode() {
        isActionMode = false
        actionIcons.removeAll()
    }
    
    private func updateActionIcons(for translation: CGSize) {
        // Update icon states based on finger proximity
        for i in actionIcons.indices {
            let iconWorldPosition = CGPoint(
                x: fingerPosition.x + actionIcons[i].position.x,
                y: fingerPosition.y + actionIcons[i].position.y
            )
            
            let distance = sqrt(
                pow(fingerPosition.x - iconWorldPosition.x, 2) +
                pow(fingerPosition.y - iconWorldPosition.y, 2)
            )
            
            actionIcons[i].isHighlighted = distance < 30
        }
    }
    
    private func handleActionModeEnd(translation: CGSize, velocity: CGSize) {
        // Find the highlighted action icon
        if let highlightedIcon = actionIcons.first(where: { $0.isHighlighted }),
           let topCard = stackViewModel.visibleCards.last {
            
            heavyHaptic.impactOccurred()
            
            switch highlightedIcon.type {
            case .delete:
                stackViewModel.deleteCard(topCard)
            case .archive:
                stackViewModel.archiveCard(topCard)
            case .favorite:
                stackViewModel.favoriteCard(topCard)
            }
        }
    }
    
    private func handleCardSwipe(translation: CGSize, velocity: CGSize, geometry: GeometryProxy) {
        let threshold: CGFloat = 100
        let velocityThreshold: CGFloat = 500
        
        guard let topCard = stackViewModel.visibleCards.last else { return }
        
        // Determine swipe direction and action
        if abs(translation.x) > threshold || abs(velocity.x) > velocityThreshold {
            if translation.x > 0 {
                // Swipe right - Favor
                stackViewModel.favoriteCard(topCard)
                mediumHaptic.impactOccurred()
            } else {
                // Swipe left - Archive
                stackViewModel.archiveCard(topCard)
                lightHaptic.impactOccurred()
            }
        } else if translation.y > threshold || velocity.y > velocityThreshold {
            // Swipe down - Defer (move to bottom of stack)
            stackViewModel.deferCard(topCard)
            lightHaptic.impactOccurred()
        } else if translation.y < -threshold || velocity.y < -velocityThreshold {
            // Swipe up - Open dossier
            selectedContact = topCard.person
            showingDossier = true
            heavyHaptic.impactOccurred()
        }
    }
    
    private func resetGestureState() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            dragOffset = .zero
            dragRotation = 0
            isActionMode = false
            actionIcons.removeAll()
            stackViewModel.hasProvidedThresholdFeedback = false
        }
    }
}

#Preview {
    LivingCardStackView(contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext))
}
