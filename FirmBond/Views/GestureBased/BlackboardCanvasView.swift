//
//  BlackboardCanvasView.swift
//  FirmBond
//
//  Revolutionary relationship mind-mapping canvas
//  Draw connections between contacts like a digital conspiracy board
//

import SwiftUI

struct BlackboardCanvasView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    @StateObject private var canvasViewModel = BlackboardViewModel()
    let onClose: () -> Void
    
    @State private var draggedContact: CanvasContact?
    @State private var connectionStart: CanvasContact?
    @State private var currentDrawingPath = Path()
    @State private var fingerPosition = CGPoint.zero
    @State private var isDrawingConnection = false
    @State private var selectedConnection: ConnectionLine?
    
    // Haptic feedback
    private let lightHaptic = UIImpactFeedbackGenerator(style: .light)
    private let mediumHaptic = UIImpactFeedbackGenerator(style: .medium)
    private let heavyHaptic = UIImpactFeedbackGenerator(style: .heavy)
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dark canvas background
                canvasBackground
                
                // Connection lines (drawn behind contacts)
                connectionLinesLayer
                
                // Active drawing line
                activeDrawingLine
                
                // Contact nodes
                contactNodesLayer(geometry: geometry)
                
                // Canvas tools overlay
                canvasToolsOverlay
                
                // Connection annotation overlay
                connectionAnnotationOverlay
            }
        }
        .onAppear {
            canvasViewModel.loadContacts(from: contactsViewModel.people)
        }
        .onChange(of: contactsViewModel.people) { newPeople in
            canvasViewModel.loadContacts(from: newPeople)
        }
        .gesture(
            twoFingerGesture
        )
    }
    
    // MARK: - Canvas Background
    private var canvasBackground: some View {
        ZStack {
            // Dark background with subtle texture
            LinearGradient(
                colors: [
                    Color.black.opacity(0.9),
                    Color.black.opacity(0.95)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Subtle grid pattern
            Canvas { context, size in
                let gridSpacing: CGFloat = 40
                context.stroke(
                    Path { path in
                        for x in stride(from: 0, through: size.width, by: gridSpacing) {
                            path.move(to: CGPoint(x: x, y: 0))
                            path.addLine(to: CGPoint(x: x, y: size.height))
                        }
                        for y in stride(from: 0, through: size.height, by: gridSpacing) {
                            path.move(to: CGPoint(x: 0, y: y))
                            path.addLine(to: CGPoint(x: size.width, y: y))
                        }
                    },
                    with: .color(.white.opacity(0.05)),
                    lineWidth: 0.5
                )
            }
        }
    }
    
    // MARK: - Connection Lines Layer
    private var connectionLinesLayer: some View {
        Canvas { context, size in
            for connection in canvasViewModel.connections {
                guard let startContact = canvasViewModel.contacts.first(where: { $0.id == connection.startContactId }),
                      let endContact = canvasViewModel.contacts.first(where: { $0.id == connection.endContactId }) else {
                    continue
                }
                
                let path = createConnectionPath(
                    from: startContact.position,
                    to: endContact.position,
                    style: connection.style
                )
                
                // Draw connection line
                context.stroke(
                    path,
                    with: .color(connection.color),
                    style: StrokeStyle(
                        lineWidth: connection.thickness,
                        lineCap: .round,
                        lineJoin: .round
                    )
                )
                
                // Draw connection label if exists
                if let label = connection.label, !label.isEmpty {
                    let midPoint = CGPoint(
                        x: (startContact.position.x + endContact.position.x) / 2,
                        y: (startContact.position.y + endContact.position.y) / 2
                    )
                    
                    context.draw(
                        Text(label)
                            .font(.caption)
                            .foregroundColor(.white),
                        at: midPoint
                    )
                }
            }
        }
    }
    
    // MARK: - Active Drawing Line
    private var activeDrawingLine: some View {
        Group {
            if isDrawingConnection,
               let startContact = connectionStart {
                Path { path in
                    path.move(to: startContact.position)
                    path.addLine(to: fingerPosition)
                }
                .stroke(
                    Color.white.opacity(0.8),
                    style: StrokeStyle(
                        lineWidth: 2,
                        lineCap: .round,
                        dash: [5, 5]
                    )
                )
            }
        }
    }
    
    // MARK: - Contact Nodes Layer
    private func contactNodesLayer(geometry: GeometryProxy) -> some View {
        ForEach(canvasViewModel.contacts) { contact in
            CanvasContactNode(
                contact: contact,
                isSelected: draggedContact?.id == contact.id,
                isDragTarget: isDrawingConnection && connectionStart?.id != contact.id
            )
            .position(contact.position)
            .gesture(
                contactGesture(for: contact, geometry: geometry)
            )
        }
    }
    
    // MARK: - Canvas Tools Overlay
    private var canvasToolsOverlay: some View {
        VStack {
            HStack {
                // Close button
                Button(action: onClose) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.8))
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.5))
                                .frame(width: 32, height: 32)
                        )
                }
                
                Spacer()
                
                // Canvas title
                Text("Relationship Canvas")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                // Tools menu
                Menu {
                    Button("Clear All Connections") {
                        canvasViewModel.clearAllConnections()
                    }
                    Button("Reset Layout") {
                        canvasViewModel.resetLayout()
                    }
                    Button("Export Image") {
                        // TODO: Implement export
                    }
                } label: {
                    Image(systemName: "ellipsis.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.8))
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.5))
                                .frame(width: 32, height: 32)
                        )
                }
            }
            .padding()
            
            Spacer()
            
            // Bottom instruction
            if canvasViewModel.connections.isEmpty {
                VStack(spacing: 8) {
                    Text("Drag contacts to position them")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text("Long press and drag between contacts to connect them")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.3))
                )
                .padding(.bottom, 40)
            }
        }
    }
    
    // MARK: - Connection Annotation Overlay
    private var connectionAnnotationOverlay: some View {
        Group {
            if let connection = selectedConnection {
                ConnectionAnnotationView(
                    connection: connection,
                    onSave: { label in
                        canvasViewModel.updateConnectionLabel(connection.id, label: label)
                        selectedConnection = nil
                    },
                    onDelete: {
                        canvasViewModel.removeConnection(connection.id)
                        selectedConnection = nil
                    },
                    onCancel: {
                        selectedConnection = nil
                    }
                )
            }
        }
    }
}

// MARK: - Gesture Handling
extension BlackboardCanvasView {
    
    private var twoFingerGesture: some Gesture {
        MagnificationGesture()
            .onChanged { value in
                // Handle canvas zoom
                canvasViewModel.canvasScale = value
            }
            .onEnded { value in
                // Snap to reasonable zoom levels
                withAnimation(.spring()) {
                    if value < 0.8 {
                        canvasViewModel.canvasScale = 0.5
                    } else if value > 1.5 {
                        canvasViewModel.canvasScale = 2.0
                    } else {
                        canvasViewModel.canvasScale = 1.0
                    }
                }
            }
    }
    
    private func contactGesture(for contact: CanvasContact, geometry: GeometryProxy) -> some Gesture {
        DragGesture()
            .onChanged { value in
                fingerPosition = value.location
                
                if draggedContact == nil {
                    // Start dragging
                    draggedContact = contact
                    lightHaptic.impactOccurred()
                }
                
                // Update contact position
                canvasViewModel.updateContactPosition(
                    contact.id,
                    position: value.location
                )
            }
            .onEnded { value in
                draggedContact = nil
                
                // Check if we're close to another contact for connection
                if let nearbyContact = canvasViewModel.findNearbyContact(
                    to: value.location,
                    excluding: contact.id,
                    threshold: 60
                ) {
                    // Create connection
                    canvasViewModel.createConnection(
                        from: contact.id,
                        to: nearbyContact.id
                    )
                    mediumHaptic.impactOccurred()
                }
            }
            .simultaneously(with:
                LongPressGesture(minimumDuration: 0.5)
                    .onChanged { pressing in
                        if pressing && !isDrawingConnection {
                            // Start connection drawing mode
                            connectionStart = contact
                            isDrawingConnection = true
                            heavyHaptic.impactOccurred()
                        }
                    }
                    .onEnded { _ in
                        // End connection drawing
                        if isDrawingConnection {
                            // Check if we ended on another contact
                            if let targetContact = canvasViewModel.findContactAt(fingerPosition),
                               targetContact.id != contact.id {
                                canvasViewModel.createConnection(
                                    from: contact.id,
                                    to: targetContact.id
                                )
                                mediumHaptic.impactOccurred()
                            }
                        }
                        
                        isDrawingConnection = false
                        connectionStart = nil
                    }
            )
    }
    
    private func createConnectionPath(from start: CGPoint, to end: CGPoint, style: ConnectionStyle) -> Path {
        Path { path in
            switch style {
            case .straight:
                path.move(to: start)
                path.addLine(to: end)
                
            case .curved:
                let controlPoint = CGPoint(
                    x: (start.x + end.x) / 2,
                    y: min(start.y, end.y) - 50
                )
                path.move(to: start)
                path.addQuadCurve(to: end, control: controlPoint)
                
            case .zigzag:
                let midX = (start.x + end.x) / 2
                path.move(to: start)
                path.addLine(to: CGPoint(x: midX, y: start.y))
                path.addLine(to: CGPoint(x: midX, y: end.y))
                path.addLine(to: end)
            }
        }
    }
}

#Preview {
    BlackboardCanvasView(
        contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext)
    ) {
        // Close action
    }
}
