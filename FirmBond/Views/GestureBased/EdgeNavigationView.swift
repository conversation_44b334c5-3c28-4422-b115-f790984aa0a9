//
//  EdgeNavigationView.swift
//  FirmBond
//
//  Edge-based navigation system for gesture interface
//

import SwiftUI

struct EdgeNavigationView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    @State private var showingMapView = false
    @State private var showingGroupsView = false
    @State private var showingSearchView = false
    @State private var showingMeView = false
    
    // Triple-tap detection for "Me" view
    @State private var tapCount = 0
    @State private var lastTapTime = Date()
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Main content - Living Card Stack
                LivingCardStackView(contactsViewModel: contactsViewModel)
                
                // Edge gesture areas
                edgeGestureAreas(geometry: geometry)
                
                // Sliding overlays
                mapOverlay(geometry: geometry)
                groupsOverlay(geometry: geometry)
                searchOverlay(geometry: geometry)
                meOverlay(geometry: geometry)
            }
        }
        .onTapGesture {
            handleTripleTap()
        }
    }
    
    // MARK: - Edge Gesture Areas
    private func edgeGestureAreas(geometry: GeometryProxy) -> some View {
        ZStack {
            // Left edge - Map
            Rectangle()
                .fill(Color.clear)
                .frame(width: 20)
                .position(x: 10, y: geometry.size.height / 2)
                .gesture(
                    DragGesture()
                        .onEnded { value in
                            if value.translation.x > 50 {
                                openMapView()
                            }
                        }
                )
            
            // Right edge - Groups
            Rectangle()
                .fill(Color.clear)
                .frame(width: 20)
                .position(x: geometry.size.width - 10, y: geometry.size.height / 2)
                .gesture(
                    DragGesture()
                        .onEnded { value in
                            if value.translation.x < -50 {
                                openGroupsView()
                            }
                        }
                )
            
            // Bottom edge - Search
            Rectangle()
                .fill(Color.clear)
                .frame(height: 20)
                .position(x: geometry.size.width / 2, y: geometry.size.height - 10)
                .gesture(
                    DragGesture()
                        .onEnded { value in
                            if value.translation.y < -50 {
                                openSearchView()
                            }
                        }
                )
        }
    }
    
    // MARK: - Map Overlay
    private func mapOverlay(geometry: GeometryProxy) -> some View {
        HStack {
            if showingMapView {
                RadialMapView(contactsViewModel: contactsViewModel) {
                    closeMapView()
                }
                .frame(width: geometry.size.width * 0.8)
                .transition(.move(edge: .leading))
            }
            Spacer()
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: showingMapView)
    }
    
    // MARK: - Groups Overlay
    private func groupsOverlay(geometry: GeometryProxy) -> some View {
        HStack {
            Spacer()
            if showingGroupsView {
                GroupsSheetView(contactsViewModel: contactsViewModel) {
                    closeGroupsView()
                }
                .frame(width: geometry.size.width * 0.7)
                .transition(.move(edge: .trailing))
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: showingGroupsView)
    }
    
    // MARK: - Search Overlay
    private func searchOverlay(geometry: GeometryProxy) -> some View {
        VStack {
            Spacer()
            if showingSearchView {
                SearchTrayView(contactsViewModel: contactsViewModel) {
                    closeSearchView()
                }
                .frame(height: geometry.size.height * 0.6)
                .transition(.move(edge: .bottom))
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: showingSearchView)
    }
    
    // MARK: - Me Overlay
    private func meOverlay(geometry: GeometryProxy) -> some View {
        if showingMeView {
            MeHubView(contactsViewModel: contactsViewModel) {
                closeMeView()
            }
            .transition(.scale.combined(with: .opacity))
            .animation(.spring(response: 0.5, dampingFraction: 0.7), value: showingMeView)
        }
    }
    
    // MARK: - Navigation Actions
    private func openMapView() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        showingMapView = true
    }
    
    private func closeMapView() {
        showingMapView = false
    }
    
    private func openGroupsView() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        showingGroupsView = true
    }
    
    private func closeGroupsView() {
        showingGroupsView = false
    }
    
    private func openSearchView() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        showingSearchView = true
    }
    
    private func closeSearchView() {
        showingSearchView = false
    }
    
    private func openMeView() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        showingMeView = true
    }
    
    private func closeMeView() {
        showingMeView = false
    }
    
    // MARK: - Triple Tap Detection
    private func handleTripleTap() {
        let now = Date()
        let timeSinceLastTap = now.timeIntervalSince(lastTapTime)
        
        if timeSinceLastTap < 0.5 {
            tapCount += 1
        } else {
            tapCount = 1
        }
        
        lastTapTime = now
        
        if tapCount >= 3 {
            openMeView()
            tapCount = 0
        }
        
        // Reset tap count after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if Date().timeIntervalSince(lastTapTime) >= 0.5 {
                tapCount = 0
            }
        }
    }
}

// MARK: - Supporting Views
struct RadialMapView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    let onClose: () -> Void
    
    var body: some View {
        ZStack {
            // Background
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.large,
                padding: DesignSystem.Spacing.lg,
                shadowStyle: .large
            ) {
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Header
                    HStack {
                        Text("Contact Map")
                            .font(DesignSystem.Typography.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                        
                        Spacer()
                        
                        Button("Close") {
                            onClose()
                        }
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                    }
                    
                    // Radial contact layout
                    GeometryReader { geometry in
                        ForEach(Array(contactsViewModel.people.prefix(8).enumerated()), id: \.element.id) { index, person in
                            let angle = Double(index) * (2 * .pi / 8)
                            let radius = min(geometry.size.width, geometry.size.height) * 0.3
                            let x = geometry.size.width / 2 + cos(angle) * radius
                            let y = geometry.size.height / 2 + sin(angle) * radius
                            
                            ContactMapNode(person: person)
                                .position(x: x, y: y)
                        }
                    }
                }
            }
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.x < -100 {
                        onClose()
                    }
                }
        )
    }
}

struct ContactMapNode: View {
    let person: Person
    
    var body: some View {
        VStack(spacing: 4) {
            Circle()
                .fill(DesignSystem.Colors.mutedGold)
                .frame(width: 40, height: 40)
                .overlay(
                    Text(person.initials)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                )
            
            Text(person.firstName ?? "")
                .font(.caption2)
                .foregroundColor(DesignSystem.Colors.warmBlack)
                .lineLimit(1)
        }
    }
}

struct GroupsSheetView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    let onClose: () -> Void
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg,
            shadowStyle: .large
        ) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.lg) {
                // Header
                HStack {
                    Text("Groups")
                        .font(DesignSystem.Typography.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Spacer()
                    
                    Button("Close") {
                        onClose()
                    }
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                }
                
                // Group categories
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                    groupCategory(title: "Work", count: 12, color: .blue)
                    groupCategory(title: "Family", count: 8, color: .green)
                    groupCategory(title: "Friends", count: 15, color: .orange)
                    groupCategory(title: "Acquaintances", count: 23, color: .purple)
                }
                
                Spacer()
            }
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.x > 100 {
                        onClose()
                    }
                }
        )
    }
    
    private func groupCategory(title: String, count: Int, color: Color) -> some View {
        HStack {
            Circle()
                .fill(color.opacity(0.2))
                .frame(width: 12, height: 12)
                .overlay(
                    Circle()
                        .fill(color)
                        .frame(width: 6, height: 6)
                )
            
            Text(title)
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Spacer()
            
            Text("\(count)")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
        .padding(DesignSystem.Spacing.sm)
    }
}

struct SearchTrayView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    let onClose: () -> Void
    
    @State private var searchText = ""
    
    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg,
            shadowStyle: .large
        ) {
            VStack(spacing: DesignSystem.Spacing.lg) {
                // Header with search
                VStack(spacing: DesignSystem.Spacing.md) {
                    HStack {
                        Text("Search & Quick Actions")
                            .font(DesignSystem.Typography.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                        
                        Spacer()
                        
                        Button("Close") {
                            onClose()
                        }
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                    }
                    
                    // Search field
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(DesignSystem.Colors.subtleText)
                        
                        TextField("Search contacts...", text: $searchText)
                            .font(DesignSystem.Typography.body)
                    }
                    .padding(DesignSystem.Spacing.md)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                            .fill(DesignSystem.Colors.primaryCream.opacity(0.5))
                    )
                }
                
                // Quick actions
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: DesignSystem.Spacing.md) {
                    quickActionButton(title: "Add Contact", icon: "person.badge.plus")
                    quickActionButton(title: "Recent", icon: "clock")
                    quickActionButton(title: "Favorites", icon: "heart")
                    quickActionButton(title: "Archive", icon: "archivebox")
                }
                
                Spacer()
            }
        }
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.y > 100 {
                        onClose()
                    }
                }
        )
    }
    
    private func quickActionButton(title: String, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(DesignSystem.Colors.mutedGold)
            
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.warmBlack)
        }
        .frame(maxWidth: .infinity)
        .padding(DesignSystem.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                .fill(DesignSystem.Colors.primaryCream.opacity(0.3))
        )
    }
}

struct MeHubView: View {
    @ObservedObject var contactsViewModel: ContactsViewModel
    let onClose: () -> Void
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    onClose()
                }
            
            // Me hub content
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.large,
                padding: DesignSystem.Spacing.xl,
                shadowStyle: .large
            ) {
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Header
                    Text("Personal Hub")
                        .font(DesignSystem.Typography.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    // Stats
                    HStack(spacing: DesignSystem.Spacing.lg) {
                        statCard(title: "Contacts", value: "\(contactsViewModel.people.count)")
                        statCard(title: "This Week", value: "8")
                        statCard(title: "Memories", value: "47")
                    }
                    
                    // Quick insights
                    Text("You've been most active with work contacts this week")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                        .multilineTextAlignment(.center)
                    
                    Button("Close") {
                        onClose()
                    }
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                }
            }
            .frame(maxWidth: 300)
        }
    }
    
    private func statCard(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(DesignSystem.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
    }
}

#Preview {
    EdgeNavigationView(contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext))
}
