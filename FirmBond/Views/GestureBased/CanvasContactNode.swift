//
//  CanvasContactNode.swift
//  FirmBond
//
//  Individual contact node for the blackboard canvas
//

import SwiftUI

struct CanvasContactNode: View {
    let contact: CanvasContact
    let isSelected: Bool
    let isDragTarget: Bool
    
    @State private var isPressed = false
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // Outer glow for drag target
            if isDragTarget {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 30,
                            endRadius: 60
                        )
                    )
                    .frame(width: 120, height: 120)
                    .scaleEffect(pulseScale)
            }
            
            // Main contact node
            ZStack {
                // Background circle with glassmorphic effect
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.4),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: Color.black.opacity(0.3),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
                
                // Contact avatar or initials
                Group {
                    if let photoData = contact.person.photoData,
                       let uiImage = UIImage(data: photoData) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        DesignSystem.Colors.mutedGold,
                                        DesignSystem.Colors.mutedGold.opacity(0.8)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 60, height: 60)
                            .overlay(
                                Text(contact.person.initials)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            )
                    }
                }
            }
            .scaleEffect(isSelected ? 1.1 : (isPressed ? 0.95 : 1.0))
            .overlay(
                // Selection indicator
                Circle()
                    .stroke(
                        Color.white,
                        lineWidth: isSelected ? 3 : 0
                    )
                    .frame(width: 90, height: 90)
                    .opacity(isSelected ? 1 : 0)
            )
            
            // Contact name label
            VStack {
                Spacer()
                
                Text(contact.person.firstName ?? "Unknown")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.black.opacity(0.6))
                    )
                    .offset(y: 50)
            }
        }
        .onAppear {
            if isDragTarget {
                startPulseAnimation()
            }
        }
        .onChange(of: isDragTarget) { newValue in
            if newValue {
                startPulseAnimation()
            } else {
                stopPulseAnimation()
            }
        }
    }
    
    private func startPulseAnimation() {
        withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
            pulseScale = 1.2
        }
    }
    
    private func stopPulseAnimation() {
        withAnimation(.easeOut(duration: 0.3)) {
            pulseScale = 1.0
        }
    }
}

struct ConnectionAnnotationView: View {
    let connection: ConnectionLine
    let onSave: (String) -> Void
    let onDelete: () -> Void
    let onCancel: () -> Void
    
    @State private var labelText: String = ""
    @State private var selectedStyle: ConnectionStyle
    @FocusState private var isTextFieldFocused: Bool
    
    init(connection: ConnectionLine, onSave: @escaping (String) -> Void, onDelete: @escaping () -> Void, onCancel: @escaping () -> Void) {
        self.connection = connection
        self.onSave = onSave
        self.onDelete = onDelete
        self.onCancel = onCancel
        self._labelText = State(initialValue: connection.label ?? "")
        self._selectedStyle = State(initialValue: connection.style)
    }
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.7)
                .ignoresSafeArea()
                .onTapGesture {
                    onCancel()
                }
            
            // Annotation panel
            VStack(spacing: 20) {
                // Header
                Text("Connection Details")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                // Label input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Relationship Label")
                        .font(.callout)
                        .foregroundColor(.white.opacity(0.8))
                    
                    TextField("e.g., 'Colleagues', 'Best Friends', 'Family'", text: $labelText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .focused($isTextFieldFocused)
                }
                
                // Style picker
                VStack(alignment: .leading, spacing: 8) {
                    Text("Connection Style")
                        .font(.callout)
                        .foregroundColor(.white.opacity(0.8))
                    
                    HStack(spacing: 12) {
                        ForEach(ConnectionStyle.allCases, id: \.self) { style in
                            Button(action: {
                                selectedStyle = style
                            }) {
                                VStack(spacing: 4) {
                                    connectionStylePreview(style)
                                    
                                    Text(style.displayName)
                                        .font(.caption)
                                        .foregroundColor(selectedStyle == style ? .white : .white.opacity(0.6))
                                }
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(selectedStyle == style ? Color.white.opacity(0.2) : Color.clear)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(
                                                    selectedStyle == style ? Color.white : Color.white.opacity(0.3),
                                                    lineWidth: 1
                                                )
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                
                // Action buttons
                HStack(spacing: 16) {
                    Button("Delete") {
                        onDelete()
                    }
                    .foregroundColor(.red)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.red.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.red, lineWidth: 1)
                            )
                    )
                    
                    Button("Cancel") {
                        onCancel()
                    }
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.white.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                    
                    Button("Save") {
                        onSave(labelText)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(DesignSystem.Colors.mutedGold)
                    )
                }
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.9))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .padding(40)
        }
        .onAppear {
            isTextFieldFocused = true
        }
    }
    
    private func connectionStylePreview(_ style: ConnectionStyle) -> some View {
        Canvas { context, size in
            let start = CGPoint(x: 10, y: size.height / 2)
            let end = CGPoint(x: size.width - 10, y: size.height / 2)
            
            let path = Path { path in
                switch style {
                case .straight:
                    path.move(to: start)
                    path.addLine(to: end)
                case .curved:
                    let control = CGPoint(x: size.width / 2, y: 5)
                    path.move(to: start)
                    path.addQuadCurve(to: end, control: control)
                case .zigzag:
                    let mid = CGPoint(x: size.width / 2, y: size.height / 2)
                    path.move(to: start)
                    path.addLine(to: CGPoint(x: size.width / 3, y: 5))
                    path.addLine(to: CGPoint(x: 2 * size.width / 3, y: size.height - 5))
                    path.addLine(to: end)
                }
            }
            
            context.stroke(
                path,
                with: .color(.white),
                style: StrokeStyle(lineWidth: 2, lineCap: .round)
            )
        }
        .frame(width: 60, height: 20)
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        CanvasContactNode(
            contact: CanvasContact(
                person: Person.preview,
                position: CGPoint(x: 200, y: 200)
            ),
            isSelected: false,
            isDragTarget: true
        )
    }
}
