//
//  ActionIconView.swift
//  FirmBond
//
//  Action icons that appear above finger during gestures
//

import SwiftUI

struct ActionIconView: View {
    let icon: ActionIcon
    let fingerPosition: CGPoint
    
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(
                    icon.isHighlighted ? 
                    icon.type.color : 
                    DesignSystem.Colors.primaryCream
                )
                .frame(width: 50, height: 50)
                .shadow(
                    color: icon.type.color.opacity(0.3),
                    radius: icon.isHighlighted ? 12 : 6,
                    x: 0,
                    y: 4
                )
            
            // Icon
            Image(systemName: icon.type.systemImage)
                .font(.title2)
                .foregroundColor(
                    icon.isHighlighted ? 
                    .white : 
                    icon.type.color
                )
                .scaleEffect(icon.isHighlighted ? 1.2 : 1.0)
        }
        .scaleEffect(pulseScale)
        .position(
            x: fingerPosition.x + icon.position.x,
            y: fingerPosition.y + icon.position.y
        )
        .animation(.easeInOut(duration: 0.2), value: icon.isHighlighted)
        .onAppear {
            startPulseAnimation()
        }
    }
    
    private func startPulseAnimation() {
        withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
            pulseScale = 1.1
        }
    }
}

struct UndoToastView: View {
    let onUndo: () -> Void
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Image(systemName: "arrow.uturn.backward")
                .font(.title3)
                .foregroundColor(DesignSystem.Colors.mutedGold)
            
            Text("Action completed")
                .font(DesignSystem.Typography.callout)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Spacer()
            
            Button("Undo") {
                onUndo()
            }
            .font(DesignSystem.Typography.callout)
            .fontWeight(.semibold)
            .foregroundColor(DesignSystem.Colors.mutedGold)
        }
        .padding(DesignSystem.Spacing.md)
        .background(
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: 0,
                shadowStyle: .medium
            ) {
                EmptyView()
            }
        )
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.top, DesignSystem.Spacing.md)
        .gesture(
            DragGesture()
                .onEnded { value in
                    if value.translation.y < -50 {
                        onUndo()
                    }
                }
        )
    }
}

struct ContactDossierView: View {
    let person: Person
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var dragOffset = CGSize.zero
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.primaryCream,
                        DesignSystem.Colors.secondaryCream
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Content
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // Header
                        dossierHeader
                        
                        // Quick stats
                        quickStatsView
                        
                        // Recent interactions
                        recentInteractionsView
                        
                        // Notes section
                        notesSection
                        
                        Spacer(minLength: 100)
                    }
                    .padding(DesignSystem.Spacing.lg)
                }
                
                // Gesture handle at top
                VStack {
                    gestureHandle
                    Spacer()
                }
            }
        }
        .offset(y: dragOffset.height)
        .gesture(
            DragGesture()
                .onChanged { value in
                    if value.translation.y > 0 {
                        dragOffset = value.translation
                    }
                }
                .onEnded { value in
                    if value.translation.y > 100 {
                        dismiss()
                    } else {
                        withAnimation(.spring()) {
                            dragOffset = .zero
                        }
                    }
                }
        )
    }
    
    private var gestureHandle: some View {
        RoundedRectangle(cornerRadius: 3)
            .fill(DesignSystem.Colors.subtleText.opacity(0.3))
            .frame(width: 40, height: 6)
            .padding(.top, 8)
    }
    
    private var dossierHeader: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Large avatar
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.mutedGold,
                                DesignSystem.Colors.mutedGold.opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Text(person.initials)
                    .font(.system(size: 48, weight: .semibold))
                    .foregroundColor(.white)
            }
            .shadow(
                color: DesignSystem.Colors.mutedGold.opacity(0.3),
                radius: 16,
                x: 0,
                y: 8
            )
            
            // Name and title
            VStack(spacing: 4) {
                Text(person.fullName)
                    .font(DesignSystem.Typography.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                if let title = person.title, !title.isEmpty {
                    Text(title)
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
    }
    
    private var quickStatsView: some View {
        HStack(spacing: DesignSystem.Spacing.lg) {
            statCard(title: "Interactions", value: "12", icon: "message")
            statCard(title: "Last Contact", value: "2d ago", icon: "clock")
            statCard(title: "Relationship", value: "Close", icon: "heart")
        }
    }
    
    private func statCard(title: String, value: String, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(DesignSystem.Colors.mutedGold)
            
            Text(value)
                .font(DesignSystem.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.subtleText)
        }
        .frame(maxWidth: .infinity)
        .padding(DesignSystem.Spacing.md)
        .background(
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: 0,
                shadowStyle: .soft
            ) {
                EmptyView()
            }
        )
    }
    
    private var recentInteractionsView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            Text("Recent Interactions")
                .font(DesignSystem.Typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            // Placeholder for interactions
            ForEach(0..<3, id: \.self) { _ in
                interactionRow
            }
        }
    }
    
    private var interactionRow: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Circle()
                .fill(DesignSystem.Colors.mutedGold.opacity(0.2))
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Coffee meeting at Blue Bottle")
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                Text("2 days ago")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }
            
            Spacer()
        }
        .padding(DesignSystem.Spacing.sm)
    }
    
    private var notesSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            Text("Notes")
                .font(DesignSystem.Typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: DesignSystem.Spacing.md,
                shadowStyle: .soft
            ) {
                Text(person.notes ?? "No notes yet. Add your first impression or memory about this person.")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.warmBlack.opacity(0.8))
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
    }
}

#Preview {
    ActionIconView(
        icon: ActionIcon(type: .favorite, position: CGPoint(x: 0, y: -40), isHighlighted: true),
        fingerPosition: CGPoint(x: 200, y: 300)
    )
}
