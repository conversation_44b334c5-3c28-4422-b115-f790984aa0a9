//
//  SettingsView.swift
//  FirmBond
//
//  Settings view for toggling interface modes
//

import SwiftUI

struct SettingsView: View {
    @Binding var useGestureInterface: Bool
    @Binding var useSpatialInterface: Bool
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.primaryCream,
                        DesignSystem.Colors.secondaryCream
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: DesignSystem.Spacing.xl) {
                        // Header
                        VStack(spacing: DesignSystem.Spacing.md) {
                            Text("Interface Settings")
                                .font(DesignSystem.Typography.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                            
                            Text("Choose your preferred interaction style")
                                .font(DesignSystem.Typography.callout)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.top, DesignSystem.Spacing.xl)
                        
                        // Interface Options
                        VStack(spacing: DesignSystem.Spacing.lg) {
                            // Gesture Interface Option
                            interfaceOption(
                                title: "Gesture-Only Interface",
                                subtitle: "Revolutionary no-tap experience",
                                description: "Navigate entirely through swipes and drags. Cards respond to your touch like physical objects.",
                                isSelected: useGestureInterface,
                                icon: "hand.draw",
                                accentColor: DesignSystem.Colors.mutedGold
                            ) {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    useGestureInterface = true
                                }
                            }
                            
                            // Traditional Interface Option
                            interfaceOption(
                                title: "Traditional Interface",
                                subtitle: "Familiar tabs and buttons",
                                description: "Standard iOS interface with tabs, lists, and spatial views.",
                                isSelected: !useGestureInterface,
                                icon: "rectangle.3.group",
                                accentColor: .blue
                            ) {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    useGestureInterface = false
                                }
                            }
                        }
                        
                        // Gesture Interface Tutorial
                        if useGestureInterface {
                            gestureGuide
                                .transition(.opacity.combined(with: .scale))
                        }
                        
                        Spacer(minLength: 100)
                    }
                    .padding(DesignSystem.Spacing.lg)
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    // MARK: - Interface Option Card
    private func interfaceOption(
        title: String,
        subtitle: String,
        description: String,
        isSelected: Bool,
        icon: String,
        accentColor: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.large,
                padding: DesignSystem.Spacing.lg,
                shadowStyle: isSelected ? .large : .medium
            ) {
                HStack(spacing: DesignSystem.Spacing.md) {
                    // Icon
                    ZStack {
                        Circle()
                            .fill(accentColor.opacity(0.2))
                            .frame(width: 60, height: 60)
                        
                        Image(systemName: icon)
                            .font(.title2)
                            .foregroundColor(accentColor)
                    }
                    
                    // Content
                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(DesignSystem.Typography.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                        
                        Text(subtitle)
                            .font(DesignSystem.Typography.callout)
                            .foregroundColor(accentColor)
                        
                        Text(description)
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    // Selection indicator
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(accentColor)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .stroke(
                    isSelected ? accentColor.opacity(0.5) : Color.clear,
                    lineWidth: 2
                )
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    // MARK: - Gesture Guide
    private var gestureGuide: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg,
            shadowStyle: .soft
        ) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                HStack {
                    Image(systemName: "hand.point.up")
                        .font(.title2)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                    
                    Text("Gesture Quick Guide")
                        .font(DesignSystem.Typography.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                }
                
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                    gestureItem(gesture: "Swipe Up", action: "Open contact details")
                    gestureItem(gesture: "Swipe Left", action: "Archive contact")
                    gestureItem(gesture: "Swipe Right", action: "Favorite contact")
                    gestureItem(gesture: "Swipe Down", action: "Defer to bottom")
                    gestureItem(gesture: "Drag Up + Icons", action: "Delete/Archive/Favorite")
                    gestureItem(gesture: "Edge Swipes", action: "Map, Groups, Search")
                    gestureItem(gesture: "Two-Finger Up", action: "Relationship canvas")
                    gestureItem(gesture: "Triple Tap", action: "Personal hub")
                }
            }
        }
    }
    
    private func gestureItem(gesture: String, action: String) -> some View {
        HStack {
            Text(gesture)
                .font(DesignSystem.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(DesignSystem.Colors.mutedGold)
                .frame(width: 100, alignment: .leading)
            
            Text("→")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.subtleText)
            
            Text(action)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Spacer()
        }
    }
}

#Preview {
    SettingsView(
        useGestureInterface: .constant(false),
        useSpatialInterface: .constant(false)
    )
}
