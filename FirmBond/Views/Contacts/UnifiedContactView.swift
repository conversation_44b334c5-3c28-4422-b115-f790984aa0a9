//
//  UnifiedContactView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import PhotosUI
import MessageUI

// MARK: - Contact Mode Enum
enum ContactMode: Equatable {
    case add
    case view(Person)
    case edit(Person)
    
    var person: Person? {
        switch self {
        case .add:
            return nil
        case .view(let person), .edit(let person):
            return person
        }
    }
    
    var isEditing: Bool {
        switch self {
        case .add, .edit:
            return true
        case .view:
            return false
        }
    }
}

struct UnifiedContactView: View {
    
    // MARK: - Properties
    @State private var currentMode: ContactMode
    @ObservedObject var viewModel: ContactsViewModel
    @Environment(\.dismiss) private var dismiss
    
    // Form fields
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var email = ""
    @State private var phoneNumber = ""
    @State private var relationship = ""
    @State private var notes = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var photoData: Data?
    @State private var hasChanges = false
    
    // UI State
    @State private var showingDeleteConfirmation = false
    @State private var showingActionSheet = false
    @State private var showingSecondaryActions = false
    @State private var primaryActionScale: CGFloat = 1.0
    @State private var isPerformingAction = false
    
    @FocusState private var focusedField: Field?
    @Namespace private var photoTransition
    @Namespace private var nameTransition
    
    // MARK: - Field Enum
    enum Field {
        case firstName, lastName, email, phone, relationship, notes
    }
    
    // MARK: - Initialization
    init(mode: ContactMode, viewModel: ContactsViewModel) {
        self._currentMode = State(initialValue: mode)
        self.viewModel = viewModel
        
        // Initialize fields based on mode
        if let person = mode.person {
            self._firstName = State(initialValue: person.firstName ?? "")
            self._lastName = State(initialValue: person.lastName ?? "")
            self._email = State(initialValue: person.email ?? "")
            self._phoneNumber = State(initialValue: person.phoneNumber ?? "")
            self._relationship = State(initialValue: person.relationship ?? "")
            self._notes = State(initialValue: person.notes ?? "")
            self._photoData = State(initialValue: person.photoData)
        }
    }
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                backgroundGradient

                // Unified content layout
                unifiedContent
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // Leading button (Back/Cancel)
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(leadingButtonTitle) {
                        handleLeadingButtonAction()
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                    .font(DesignSystem.Typography.callout)
                }

                // Trailing button (Edit/Save)
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(trailingButtonTitle) {
                        handleTrailingButtonAction()
                    }
                    .foregroundColor(trailingButtonColor)
                    .font(DesignSystem.Typography.callout)
                    .fontWeight(.medium)
                    .disabled(!trailingButtonEnabled)
                }
            }
        }
        .sheet(isPresented: $showingActionSheet) {
            actionSheet
        }
        .confirmationDialog(
            "Delete Contact",
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive) {
                if let person = currentMode.person {
                    viewModel.deletePerson(person)
                    dismiss()
                }
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            if let person = currentMode.person {
                Text("Are you sure you want to delete \(person.fullName)? This action cannot be undone.")
            }
        }
        .onChange(of: selectedPhoto) { _, newPhoto in
            Task {
                if let newPhoto = newPhoto,
                   let data = try? await newPhoto.loadTransferable(type: Data.self) {
                    photoData = data
                    if case .edit = currentMode {
                        hasChanges = true
                    }
                }
            }
        }
        .onChange(of: firstName) { _, _ in trackChanges() }
        .onChange(of: lastName) { _, _ in trackChanges() }
        .onChange(of: email) { _, _ in trackChanges() }
        .onChange(of: phoneNumber) { _, _ in trackChanges() }
        .onChange(of: relationship) { _, _ in trackChanges() }
        .onChange(of: notes) { _, _ in trackChanges() }
    }
    
    // MARK: - Private Views
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream,
                DesignSystem.Colors.warmGray.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Navigation
    private var navigationTitle: String {
        switch currentMode {
        case .add:
            return "New Contact"
        case .edit:
            return "Edit Contact"
        case .view(let person):
            return person.fullName
        }
    }

    private var leadingButtonTitle: String {
        switch currentMode {
        case .add, .edit:
            return "Cancel"
        case .view:
            return "Back"
        }
    }

    private var trailingButtonTitle: String {
        switch currentMode {
        case .add:
            return "Save"
        case .edit:
            return "Save"
        case .view:
            return "Edit"
        }
    }

    private var trailingButtonColor: Color {
        switch currentMode {
        case .add, .edit:
            return canSave ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.lightGray
        case .view:
            return DesignSystem.Colors.mutedGold
        }
    }

    private var trailingButtonEnabled: Bool {
        switch currentMode {
        case .add, .edit:
            return canSave
        case .view:
            return true
        }
    }

    // MARK: - Unified Content Layout
    private var unifiedContent: some View {
        Group {
            if case .add = currentMode {
                // Add mode uses form layout
                addModeContent
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .move(edge: .bottom).combined(with: .opacity)
                    ))
            } else {
                // View and edit modes use beautiful layout with dossier-style transitions
                beautifulViewContent
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
            }
        }
        .animation(DesignSystem.Animations.spring, value: currentMode)
    }

    // MARK: - Add Mode Content (Form Layout)
    private var addModeContent: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.xl) {
                // Photo section
                photoSection

                // Form fields for adding
                addFormFields

                // Save button
                saveButton

                // Bottom padding
                Spacer()
                    .frame(height: DesignSystem.Spacing.xl)
            }
            .padding(DesignSystem.Spacing.lg)
        }
    }

    // MARK: - Beautiful View Content (Hero + Cards)
    private var beautifulViewContent: some View {
        ScrollView {
            VStack(spacing: 0) {
                // Hero section with photo and name
                heroSection
                    .frame(height: 350)

                // Content cards with editable fields
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Smart action buttons (only in view mode)
                    if !currentMode.isEditing {
                        smartActionButtons
                    }

                    // Contact information cards (editable in edit mode)
                    editableContactCards

                    // Notes card (editable in edit mode)
                    editableNotesCard

                    // Activity card (view mode only)
                    if !currentMode.isEditing {
                        activityCard
                    }

                    // Bottom padding
                    Spacer()
                        .frame(height: DesignSystem.Spacing.xl)
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.lg)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.extraLarge)
                        .fill(DesignSystem.Colors.primaryCream)
                        .shadow(
                            color: DesignSystem.Colors.glassShadow,
                            radius: 20,
                            x: 0,
                            y: -10
                        )
                )
                .offset(y: -DesignSystem.Spacing.xl)
            }
        }
        .ignoresSafeArea(edges: .top)
    }

    // MARK: - Add Form Fields (Simple Form Layout)
    private var addFormFields: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // Name fields
            HStack(spacing: DesignSystem.Spacing.md) {
                ConsistentFormField(
                    title: "First Name",
                    text: $firstName,
                    placeholder: "John",
                    isRequired: true,
                    isEditable: true,
                    icon: "person"
                )
                .focused($focusedField, equals: .firstName)

                ConsistentFormField(
                    title: "Last Name",
                    text: $lastName,
                    placeholder: "Doe",
                    isRequired: true,
                    isEditable: true,
                    icon: "person"
                )
                .focused($focusedField, equals: .lastName)
            }

            // Contact information
            ConsistentFormField(
                title: "Email",
                text: $email,
                placeholder: "<EMAIL>",
                keyboardType: .emailAddress,
                isEditable: true,
                icon: "envelope"
            )
            .focused($focusedField, equals: .email)

            ConsistentFormField(
                title: "Phone",
                text: $phoneNumber,
                placeholder: "(*************",
                keyboardType: .phonePad,
                isEditable: true,
                icon: "phone"
            )
            .focused($focusedField, equals: .phone)

            // Relationship
            ConsistentFormField(
                title: "Relationship",
                text: $relationship,
                placeholder: "Friend, Colleague, Family...",
                isEditable: true,
                icon: "person.2"
            )
            .focused($focusedField, equals: .relationship)

            // Notes
            ConsistentFormField(
                title: "Notes",
                text: $notes,
                placeholder: "Additional notes...",
                isMultiline: true,
                isEditable: true,
                icon: "note.text"
            )
            .focused($focusedField, equals: .notes)
        }
    }

    // MARK: - Hero Section (Beautiful Layout)
    private var heroSection: some View {
        ZStack {
            // Background with photo or gradient
            Group {
                if let person = currentMode.person,
                   let photoData = person.photoData ?? self.photoData,
                   let uiImage = UIImage(data: photoData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .clipped()
                        .matchedGeometryEffect(id: "contactPhoto", in: photoTransition)
                } else {
                    LinearGradient(
                        colors: [
                            DesignSystem.Colors.mutedGold,
                            DesignSystem.Colors.sageGreen
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                }
            }

            // Gradient overlay
            LinearGradient(
                colors: [
                    Color.clear,
                    Color.black.opacity(0.7)
                ],
                startPoint: .center,
                endPoint: .bottom
            )

            // Content - centered
            VStack(spacing: DesignSystem.Spacing.lg) {
                Spacer()

                // Profile photo (circular overlay for no-photo contacts)
                if (currentMode.person?.photoData ?? photoData) == nil {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 120, height: 120)
                        .overlay(
                            Text(currentMode.person?.initials ?? "\(firstName.prefix(1))\(lastName.prefix(1))")
                                .font(.system(size: 42, weight: .bold))
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        )
                        .shadow(
                            color: Color.black.opacity(0.3),
                            radius: 15,
                            x: 0,
                            y: 8
                        )
                        .matchedGeometryEffect(id: "contactPhoto", in: photoTransition)
                }

                // Name and relationship - editable in edit mode
                VStack(spacing: DesignSystem.Spacing.sm) {
                    if currentMode.isEditing {
                        // Editable name fields in hero
                        HStack(spacing: DesignSystem.Spacing.sm) {
                            TextField("First", text: $firstName)
                                .font(.custom("Georgia", size: 32, relativeTo: .largeTitle))
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .textFieldStyle(PlainTextFieldStyle())
                                .focused($focusedField, equals: .firstName)

                            TextField("Last", text: $lastName)
                                .font(.custom("Georgia", size: 32, relativeTo: .largeTitle))
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .textFieldStyle(PlainTextFieldStyle())
                                .focused($focusedField, equals: .lastName)
                        }
                        .shadow(color: .black.opacity(0.5), radius: 3, x: 0, y: 2)

                        TextField("Relationship", text: $relationship)
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .textFieldStyle(PlainTextFieldStyle())
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                            .focused($focusedField, equals: .relationship)
                    } else {
                        // Display mode with serif typography for emotional warmth
                        let displayName = currentMode.person?.fullName ?? "\(firstName) \(lastName)"
                        Text(displayName)
                            .font(.custom("Georgia", size: 32, relativeTo: .largeTitle))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .shadow(color: .black.opacity(0.5), radius: 3, x: 0, y: 2)
                            .matchedGeometryEffect(id: "contactName", in: nameTransition)

                        let displayRelationship = currentMode.person?.relationship ?? relationship
                        if !displayRelationship.isEmpty {
                            Text(displayRelationship)
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                        }
                    }
                }

                Spacer()
                    .frame(height: DesignSystem.Spacing.xxl)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
        }
    }

    // MARK: - Editable Contact Cards (Beautiful Layout)
    private var editableContactCards: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Phone card
            EditableContactCard(
                icon: "phone",
                title: "Phone",
                text: $phoneNumber,
                placeholder: "(*************",
                keyboardType: .phonePad,
                color: DesignSystem.Colors.sageGreen,
                isEditable: currentMode.isEditing
            ) {
                // Action for view mode
                if !currentMode.isEditing, !phoneNumber.isEmpty {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            }
            .focused($focusedField, equals: .phone)

            // Email card
            EditableContactCard(
                icon: "envelope",
                title: "Email",
                text: $email,
                placeholder: "<EMAIL>",
                keyboardType: .emailAddress,
                color: DesignSystem.Colors.mutedGold,
                isEditable: currentMode.isEditing
            ) {
                // Action for view mode
                if !currentMode.isEditing, !email.isEmpty {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            }
            .focused($focusedField, equals: .email)
        }
    }

    // MARK: - Editable Notes Card
    private var editableNotesCard: some View {
        EditableNotesCard(
            text: $notes,
            placeholder: "Add notes about this person...",
            isEditable: currentMode.isEditing
        )
        .focused($focusedField, equals: .notes)
    }

    // MARK: - Smart Action Buttons (View Mode)
    private var smartActionButtons: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            if let primaryAction = getPrimaryAction() {
                SmartPrimaryActionButton(
                    action: primaryAction,
                    isPerforming: $isPerformingAction,
                    scale: $primaryActionScale
                ) {
                    performPrimaryAction(primaryAction)
                }
            }

            // Secondary actions with improved animations
            if hasSecondaryActions() {
                Button(action: {
                    withAnimation(DesignSystem.Animations.spring) {
                        showingSecondaryActions.toggle()
                    }

                    // Haptic feedback for expansion
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        Text(showingSecondaryActions ? "Less" : "More actions")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.mutedGold)

                        Image(systemName: showingSecondaryActions ? "chevron.up" : "chevron.down")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.mutedGold)
                            .rotationEffect(.degrees(showingSecondaryActions ? 180 : 0))
                    }
                    .padding(.vertical, DesignSystem.Spacing.xs)
                }
                .buttonStyle(PlainButtonStyle())

                if showingSecondaryActions {
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        ForEach(getSecondaryActions(), id: \.title) { action in
                            SecondaryActionButton(action: action) {
                                performAction(action)
                            }
                        }
                    }
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity),
                        removal: .scale(scale: 0.8).combined(with: .opacity)
                    ))
                }
            }
        }
    }

    // MARK: - Activity Card (View Mode)
    private var activityCard: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg
        ) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                HStack {
                    Image(systemName: "clock")
                        .font(.title3)
                        .foregroundColor(DesignSystem.Colors.mutedGold)

                    Text("Recent Activity")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.warmBlack)

                    Spacer()
                }

                Text("No recent activity")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }
        }
    }



    // MARK: - Photo Section
    private var photoSection: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // Photo display/picker
            Group {
                if currentMode.isEditing {
                    // Editable photo picker
                    PhotosPicker(
                        selection: $selectedPhoto,
                        matching: .images,
                        photoLibrary: .shared()
                    ) {
                        photoView
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    // Non-editable photo display
                    photoView
                }
            }

            // Name display (in view mode) with serif typography
            if !currentMode.isEditing, let person = currentMode.person {
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Text(person.fullName)
                        .font(.custom("Georgia", size: 28, relativeTo: .title))
                        .fontWeight(.bold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                        .multilineTextAlignment(.center)
                        .matchedGeometryEffect(id: "contactName", in: nameTransition)

                    if let relationship = person.relationship, !relationship.isEmpty {
                        Text(relationship)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.subtleText)
                            .multilineTextAlignment(.center)
                    }
                }
            }
        }
    }

    private var photoView: some View {
        ZStack {
            if let photoData = photoData,
               let uiImage = UIImage(data: photoData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 140, height: 140)
                    .clipShape(Circle())
                    .matchedGeometryEffect(id: "contactPhoto", in: photoTransition)
            } else {
                Circle()
                    .fill(DesignSystem.Colors.mutedGold.opacity(0.3))
                    .frame(width: 140, height: 140)
                    .matchedGeometryEffect(id: "contactPhoto", in: photoTransition)
                    .overlay(
                        VStack(spacing: DesignSystem.Spacing.xs) {
                            Image(systemName: currentMode.isEditing ? "camera.fill" : "person.circle")
                                .font(currentMode.isEditing ? .title : .system(size: 40))
                                .foregroundColor(DesignSystem.Colors.mutedGold)

                            if currentMode.isEditing {
                                Text("Add Photo")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.subtleText)
                            }
                        }
                    )
            }

            // Edit overlay for edit mode
            if currentMode.isEditing && photoData != nil {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(DesignSystem.Colors.mutedGold)
                            .frame(width: 32, height: 32)
                            .overlay(
                                Image(systemName: "pencil")
                                    .font(.caption)
                                    .foregroundColor(.white)
                            )
                            .offset(x: -10, y: -10)
                    }
                }
                .frame(width: 140, height: 140)
            }
        }
        .overlay(
            Circle()
                .stroke(DesignSystem.Colors.glassBorder, lineWidth: 2)
        )
        .shadow(
            color: DesignSystem.Colors.glassShadow,
            radius: 8,
            x: 0,
            y: 4
        )
    }



    // MARK: - Save Button
    private var saveButton: some View {
        Button(action: saveContact) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: currentMode == .add ? "person.badge.plus" : "checkmark.circle.fill")
                Text(currentMode == .add ? "Create Contact" : "Save Changes")
            }
            .font(DesignSystem.Typography.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(
                        canSave ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.lightGray
                    )
            )
        }
        .disabled(!canSave)
        .buttonStyle(PlainButtonStyle())
        .animation(DesignSystem.Animations.gentle, value: canSave)
    }



    // MARK: - Computed Properties
    private var canSave: Bool {
        let hasRequiredFields = !firstName.trimmingCharacters(in: .whitespaces).isEmpty &&
                               !lastName.trimmingCharacters(in: .whitespaces).isEmpty

        switch currentMode {
        case .add:
            return hasRequiredFields
        case .edit:
            return hasRequiredFields && hasChanges
        case .view:
            return false
        }
    }

    // MARK: - Navigation Button Handlers
    private func handleLeadingButtonAction() {
        switch currentMode {
        case .add, .view:
            dismiss()
        case .edit:
            handleCancel()
        }
    }

    private func handleTrailingButtonAction() {
        switch currentMode {
        case .add, .edit:
            saveContact()
        case .view(let person):
            // Switch to edit mode with dossier-style animation
            withAnimation(DesignSystem.Animations.spring) {
                currentMode = .edit(person)
            }

            // Initialize fields after animation starts
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                initializeEditingFields(for: person)
            }

            // Haptic feedback for mode transition
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
    }

    // MARK: - Private Methods
    private func saveContact() {
        guard canSave else { return }

        switch currentMode {
        case .add:
            viewModel.addPerson(
                firstName: firstName,
                lastName: lastName,
                email: email.isEmpty ? nil : email,
                phoneNumber: phoneNumber.isEmpty ? nil : phoneNumber,
                relationship: relationship.isEmpty ? nil : relationship,
                notes: notes.isEmpty ? nil : notes,
                photoData: photoData
            )
        case .edit(let person):
            viewModel.updatePerson(
                person,
                firstName: firstName,
                lastName: lastName,
                email: email.isEmpty ? nil : email,
                phoneNumber: phoneNumber.isEmpty ? nil : phoneNumber,
                relationship: relationship.isEmpty ? nil : relationship,
                notes: notes.isEmpty ? nil : notes,
                photoData: photoData
            )
            // Switch back to view mode after saving with smooth animation
            withAnimation(DesignSystem.Animations.spring) {
                currentMode = .view(person)
                hasChanges = false
            }
        case .view:
            return
        }

        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Only dismiss for add mode, edit mode stays open
        if case .add = currentMode {
            dismiss()
        }
    }

    private func handleCancel() {
        switch currentMode {
        case .add:
            dismiss()
        case .edit(let person):
            if hasChanges {
                // Could show confirmation dialog here
                // For now, just revert to view mode with smooth animation
                withAnimation(DesignSystem.Animations.spring) {
                    currentMode = .view(person)
                }

                // Reset fields after animation starts
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    initializeEditingFields(for: person)
                }
            } else {
                withAnimation(DesignSystem.Animations.spring) {
                    currentMode = .view(person)
                }
            }

            // Haptic feedback for cancellation
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        case .view:
            dismiss()
        }
    }

    private func trackChanges() {
        if case .edit = currentMode {
            hasChanges = true
        }
    }

    private func initializeEditingFields(for person: Person) {
        firstName = person.firstName ?? ""
        lastName = person.lastName ?? ""
        email = person.email ?? ""
        phoneNumber = person.phoneNumber ?? ""
        relationship = person.relationship ?? ""
        notes = person.notes ?? ""
        photoData = person.photoData
        hasChanges = false
    }



    // MARK: - Action Sheet
    private var actionSheet: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            if let person = currentMode.person {
                Text(person.fullName)
                    .font(DesignSystem.Typography.largeTitle)
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                VStack(spacing: DesignSystem.Spacing.md) {
                    Button("Share Contact") {
                        shareContact()
                        showingActionSheet = false
                    }
                    .foregroundColor(DesignSystem.Colors.mutedGold)

                    Button("Delete Contact") {
                        showingDeleteConfirmation = true
                        showingActionSheet = false
                    }
                    .foregroundColor(.red)

                    Button("Cancel") {
                        showingActionSheet = false
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
        .padding(DesignSystem.Spacing.lg)
    }
}

// MARK: - Smart Action Logic Extension
extension UnifiedContactView {

    // MARK: - Smart Action Detection
    private func getPrimaryAction() -> QuickAction? {
        guard let person = currentMode.person else { return nil }

        // Priority: Call > Message > Email (based on immediacy and emotional connection)
        // Fixed: Proper hierarchy - only one primary action

        // 1. Call action (highest priority) - if phone number exists, calling is primary
        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            return QuickAction(
                icon: "phone.fill",
                title: "Call \(person.firstName ?? "Contact")",
                color: DesignSystem.Colors.sageGreen,
                action: {
                    if let url = URL(string: "tel://\(phoneNumber)") {
                        UIApplication.shared.open(url)
                    }
                }
            )
        }

        // 2. Email action (if no phone number available)
        if let email = person.email, !email.isEmpty {
            return QuickAction(
                icon: "envelope.fill",
                title: "Email \(person.firstName ?? "Contact")",
                color: DesignSystem.Colors.mutedGold,
                action: {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            )
        }

        return nil
    }

    private func getSecondaryActions() -> [QuickAction] {
        guard let person = currentMode.person else { return [] }

        var actions: [QuickAction] = []
        let primaryAction = getPrimaryAction()

        // Add all available actions except the primary one
        // Fixed: Proper secondary action logic without duplicates

        if let phoneNumber = person.phoneNumber, !phoneNumber.isEmpty {
            // If call is not primary, add call as secondary
            if primaryAction?.icon != "phone.fill" {
                actions.append(QuickAction(
                    icon: "phone.fill",
                    title: "Call",
                    color: DesignSystem.Colors.sageGreen,
                    action: {
                        if let url = URL(string: "tel://\(phoneNumber)") {
                            UIApplication.shared.open(url)
                        }
                    }
                ))
            }

            // Always add message as secondary if phone exists (since call is primary)
            if primaryAction?.icon == "phone.fill" {
                actions.append(QuickAction(
                    icon: "message.fill",
                    title: "Message",
                    color: DesignSystem.Colors.softBlue,
                    action: {
                        if let url = URL(string: "sms://\(phoneNumber)") {
                            UIApplication.shared.open(url)
                        }
                    }
                ))
            }
        }

        // Add email if not primary
        if let email = person.email, !email.isEmpty, primaryAction?.icon != "envelope.fill" {
            actions.append(QuickAction(
                icon: "envelope.fill",
                title: "Email",
                color: DesignSystem.Colors.mutedGold,
                action: {
                    if let url = URL(string: "mailto:\(email)") {
                        UIApplication.shared.open(url)
                    }
                }
            ))
        }

        return actions
    }

    private func hasSecondaryActions() -> Bool {
        return !getSecondaryActions().isEmpty
    }

    private func performPrimaryAction(_ action: QuickAction) {
        // Haptic feedback for primary action
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Visual feedback
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            primaryActionScale = 0.95
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                primaryActionScale = 1.0
            }
        }

        // Perform action
        action.action()
    }

    private func performAction(_ action: QuickAction) {
        // Lighter haptic for secondary actions
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        action.action()
    }

    private func shareContact() {
        // Implement contact sharing
        guard let person = currentMode.person else { return }

        // Create a simple vCard or share basic info
        let shareText = """
        \(person.fullName)
        \(person.email ?? "")
        \(person.phoneNumber ?? "")
        """

        let activityVC = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

// MARK: - Supporting Structures
struct QuickAction {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
}

// MARK: - Consistent Form Field Component (For Add Mode)
struct ConsistentFormField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isRequired: Bool
    let isMultiline: Bool
    let isEditable: Bool
    let icon: String?

    @FocusState private var isFocused: Bool

    init(
        title: String,
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isRequired: Bool = false,
        isMultiline: Bool = false,
        isEditable: Bool = true,
        icon: String? = nil
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isRequired = isRequired
        self.isMultiline = isMultiline
        self.isEditable = isEditable
        self.icon = icon
    }

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            HStack {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                        .frame(width: 16)
                }

                Text(title)
                    .font(DesignSystem.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                if isRequired && isEditable {
                    Text("*")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(.red)
                }

                Spacer()
            }

            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.medium,
                padding: DesignSystem.Spacing.md,
                shadowStyle: (isFocused && isEditable) ? .medium : .soft
            ) {
                if isEditable {
                    // Editable field
                    if isMultiline {
                        TextField(placeholder, text: $text, axis: .vertical)
                            .lineLimit(3...6)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .keyboardType(keyboardType)
                            .focused($isFocused)
                    } else {
                        TextField(placeholder, text: $text)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .keyboardType(keyboardType)
                            .focused($isFocused)
                    }
                } else {
                    // Display-only field
                    HStack {
                        if text.isEmpty {
                            Text(placeholder)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                        } else {
                            Text(text)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        }
                        Spacer()
                    }
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(
                        (isFocused && isEditable) ? DesignSystem.Colors.mutedGold : Color.clear,
                        lineWidth: 2
                    )
            )
            .animation(DesignSystem.Animations.gentle, value: isFocused)
        }
    }
}

// MARK: - Editable Contact Card Component (For Beautiful Layout)
struct EditableContactCard: View {
    let icon: String
    let title: String
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let color: Color
    let isEditable: Bool
    let action: () -> Void

    @FocusState private var isFocused: Bool

    var body: some View {
        GlassmorphicCard(
            cornerRadius: DesignSystem.CornerRadius.large,
            padding: DesignSystem.Spacing.lg,
            shadowStyle: isFocused ? .medium : .soft
        ) {
            HStack(spacing: DesignSystem.Spacing.md) {
                // Icon
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Image(systemName: icon)
                            .font(.title3)
                            .foregroundColor(color)
                    )

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(title)
                        .font(DesignSystem.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(DesignSystem.Colors.warmBlack)

                    if isEditable {
                        TextField(placeholder, text: $text)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .keyboardType(keyboardType)
                            .focused($isFocused)
                    } else {
                        if text.isEmpty {
                            Text(placeholder)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                        } else {
                            Text(text)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                        }
                    }
                }

                Spacer()

                // Action button (only in view mode with content)
                if !isEditable && !text.isEmpty {
                    Button(action: action) {
                        Circle()
                            .fill(color.opacity(0.1))
                            .frame(width: 32, height: 32)
                            .overlay(
                                Image(systemName: "arrow.up.right")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(color)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .overlay(
            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                .stroke(
                    isFocused ? color : Color.clear,
                    lineWidth: 2
                )
        )
        .animation(DesignSystem.Animations.gentle, value: isFocused)
        .contentShape(Rectangle())
        .onTapGesture {
            if !isEditable && !text.isEmpty {
                // Haptic feedback for contact action
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                action()
            }
        }
    }
}

// MARK: - Editable Notes Card Component
struct EditableNotesCard: View {
    @Binding var text: String
    let placeholder: String
    let isEditable: Bool

    @FocusState private var isFocused: Bool

    var body: some View {
        if isEditable || !text.isEmpty {
            GlassmorphicCard(
                cornerRadius: DesignSystem.CornerRadius.large,
                padding: DesignSystem.Spacing.lg,
                shadowStyle: isFocused ? .medium : .soft
            ) {
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                    HStack {
                        Image(systemName: "note.text")
                            .font(.title3)
                            .foregroundColor(DesignSystem.Colors.mutedGold)

                        Text("Notes")
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.warmBlack)

                        Spacer()
                    }

                    if isEditable {
                        TextField(placeholder, text: $text, axis: .vertical)
                            .lineLimit(3...8)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .focused($isFocused)
                    } else {
                        if text.isEmpty {
                            Text(placeholder)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                        } else {
                            Text(text)
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.warmBlack)
                                .lineLimit(nil)
                        }
                    }
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .stroke(
                        isFocused ? DesignSystem.Colors.mutedGold : Color.clear,
                        lineWidth: 2
                    )
            )
            .animation(DesignSystem.Animations.gentle, value: isFocused)
        }
    }
}

// MARK: - Action Button Components
struct SmartPrimaryActionButton: View {
    let action: QuickAction
    @Binding var isPerforming: Bool
    @Binding var scale: CGFloat
    let onTap: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: action.icon)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(action.color)

                Text(action.title)
                    .font(DesignSystem.Typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                Spacer()
            }
            .padding(DesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .stroke(action.color.opacity(0.3), lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(scale)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

struct SecondaryActionButton: View {
    let action: QuickAction
    let onTap: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Image(systemName: action.icon)
                    .font(.callout)
                    .foregroundColor(action.color)
                    .frame(width: 20)

                Text(action.title)
                    .font(DesignSystem.Typography.callout)
                    .foregroundColor(DesignSystem.Colors.warmBlack)

                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.sm)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .fill(.ultraThinMaterial)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.easeInOut) {
                isPressed = pressing
            }

            // Haptic feedback
            if pressing {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        } perform: {}
    }
}

struct ContactInfoCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    let action: (() -> Void)?

    init(icon: String, title: String, value: String, color: Color, action: (() -> Void)? = nil) {
        self.icon = icon
        self.title = title
        self.value = value
        self.color = color
        self.action = action
    }

    var body: some View {
        Button(action: action ?? {}) {
            GlassmorphicCard {
                HStack(spacing: DesignSystem.Spacing.md) {
                    Circle()
                        .fill(color.opacity(0.15))
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: icon)
                                .font(.title3)
                                .foregroundColor(color)
                        )

                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(DesignSystem.Typography.caption)
                            .fontWeight(.medium)
                            .foregroundColor(DesignSystem.Colors.subtleText)

                        Text(value)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                            .lineLimit(2)
                    }

                    Spacer()

                    if action != nil {
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(action == nil)
    }
}

struct FloatingActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            Circle()
                .fill(color)
                .frame(width: 56, height: 56)
                .overlay(
                    Image(systemName: icon)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                )
                .shadow(
                    color: color.opacity(0.4),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(DesignSystem.Animations.spring) {
                isPressed = pressing
            }

            // Haptic feedback
            if pressing {
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }
        } perform: {}
    }
}

// MARK: - Preview
#Preview {
    UnifiedContactView(
        mode: .add,
        viewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext)
    )
}
