//
//  RelationshipLineView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct RelationshipLineView: View {
    let fromContact: SpatialContact
    let toContact: SpatialContact
    let relationship: ContactRelationship
    @ObservedObject var spatialViewModel: SpatialViewModel
    let onTap: () -> Void
    
    @State private var animationOffset: CGFloat = 0
    @State private var isHighlighted: Bool = false
    
    var body: some View {
        ZStack {
            // Main relationship line
            relationshipPath
                .stroke(
                    relationship.color.opacity(isHighlighted ? 0.9 : 0.6),
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round,
                        dash: dashPattern
                    )
                )
                .shadow(
                    color: relationship.color.opacity(0.3),
                    radius: isHighlighted ? 4 : 2,
                    x: 0,
                    y: 1
                )
            
            // Animated flow effect for strong relationships
            if relationship.strength > 0.7 {
                relationshipPath
                    .trim(from: 0, to: 0.3)
                    .stroke(
                        relationship.color.opacity(0.8),
                        style: StrokeStyle(
                            lineWidth: lineWidth * 0.5,
                            lineCap: .round
                        )
                    )
                    .offset(x: animationOffset)
                    .opacity(0.7)
            }
            
            // Relationship strength indicator (midpoint)
            strengthIndicator
        }
        .contentShape(relationshipPath)
        .onTapGesture {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isHighlighted.toggle()
            }
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            onTap()
        }
        .onAppear {
            startAnimation()
        }
    }
    
    // MARK: - Computed Properties
    
    private var relationshipPath: Path {
        Path { path in
            let startPoint = fromContact.position
            let endPoint = toContact.position
            
            // Create a curved line for more organic feel
            let distance = sqrt(
                pow(endPoint.x - startPoint.x, 2) +
                pow(endPoint.y - startPoint.y, 2)
            )
            
            // Curve intensity based on distance
            let curveIntensity = min(distance * 0.2, 50)
            
            // Calculate control points for smooth curve
            let midX = (startPoint.x + endPoint.x) / 2
            let midY = (startPoint.y + endPoint.y) / 2
            
            // Perpendicular offset for curve
            let angle = atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x)
            let perpAngle = angle + .pi / 2
            
            let controlPoint = CGPoint(
                x: midX + cos(perpAngle) * curveIntensity,
                y: midY + sin(perpAngle) * curveIntensity
            )
            
            path.move(to: startPoint)
            path.addQuadCurve(to: endPoint, control: controlPoint)
        }
    }
    
    private var lineWidth: CGFloat {
        let baseWidth: CGFloat = 2
        let strengthMultiplier = 1 + (relationship.strength * 2) // 1-3x multiplier
        return baseWidth * strengthMultiplier
    }
    
    private var dashPattern: [CGFloat] {
        switch relationship.type {
        case .family, .romantic:
            return [] // Solid line for close relationships
        case .friend:
            return [8, 4] // Dashed for friends
        case .colleague:
            return [4, 4] // Short dashes for work
        case .acquaintance:
            return [2, 6] // Dotted for acquaintances
        case .mentor:
            return [12, 4, 4, 4] // Dash-dot for mentors
        case .neighbor:
            return [6, 6] // Medium dashes for neighbors
        }
    }
    
    private var strengthIndicator: some View {
        let midPoint = calculateMidPoint()
        
        return Circle()
            .fill(relationship.color)
            .frame(width: strengthIndicatorSize, height: strengthIndicatorSize)
            .position(midPoint)
            .opacity(isHighlighted ? 1.0 : 0.7)
            .scaleEffect(isHighlighted ? 1.2 : 1.0)
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 1)
                    .position(midPoint)
                    .scaleEffect(isHighlighted ? 1.2 : 1.0)
            )
    }
    
    private var strengthIndicatorSize: CGFloat {
        let baseSize: CGFloat = 8
        return baseSize + (relationship.strength * 6) // 8-14 points
    }
    
    // MARK: - Helper Methods
    
    private func calculateMidPoint() -> CGPoint {
        let startPoint = fromContact.position
        let endPoint = toContact.position
        
        // Calculate the actual midpoint of the curved path
        let distance = sqrt(
            pow(endPoint.x - startPoint.x, 2) +
            pow(endPoint.y - startPoint.y, 2)
        )
        
        let curveIntensity = min(distance * 0.2, 50)
        let midX = (startPoint.x + endPoint.x) / 2
        let midY = (startPoint.y + endPoint.y) / 2
        
        let angle = atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x)
        let perpAngle = angle + .pi / 2
        
        // Offset the midpoint slightly along the curve
        return CGPoint(
            x: midX + cos(perpAngle) * (curveIntensity * 0.5),
            y: midY + sin(perpAngle) * (curveIntensity * 0.5)
        )
    }
    
    private func startAnimation() {
        guard relationship.strength > 0.7 else { return }
        
        withAnimation(
            .linear(duration: 2.0)
            .repeatForever(autoreverses: false)
        ) {
            animationOffset = 100
        }
    }
}

// MARK: - Relationship Creation Helper

struct RelationshipCreationOverlay: View {
    let fromContact: SpatialContact
    @Binding var dragPosition: CGPoint
    let isActive: Bool
    
    var body: some View {
        if isActive {
            Path { path in
                path.move(to: fromContact.position)
                path.addLine(to: dragPosition)
            }
            .stroke(
                Color.blue.opacity(0.6),
                style: StrokeStyle(
                    lineWidth: 3,
                    lineCap: .round,
                    dash: [8, 4]
                )
            )
            .animation(.easeInOut(duration: 0.1), value: dragPosition)
        }
    }
}

// MARK: - Relationship Detail Sheet

struct RelationshipDetailSheet: View {
    @Binding var relationship: ContactRelationship
    @ObservedObject var spatialViewModel: SpatialViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedType: RelationshipType
    @State private var strengthValue: Double
    
    init(relationship: Binding<ContactRelationship>, spatialViewModel: SpatialViewModel) {
        self._relationship = relationship
        self.spatialViewModel = spatialViewModel
        self._selectedType = State(initialValue: relationship.wrappedValue.type)
        self._strengthValue = State(initialValue: relationship.wrappedValue.strength)
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("Relationship Details")
                        .font(.custom("Georgia", size: 24, relativeTo: .title))
                        .fontWeight(.bold)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Text("Define the connection between contacts")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                        .multilineTextAlignment(.center)
                }
                .padding(.top)
                
                // Relationship Type
                VStack(alignment: .leading, spacing: 12) {
                    Text("Relationship Type")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ForEach(RelationshipType.allCases, id: \.self) { type in
                            RelationshipTypeCard(
                                type: type,
                                isSelected: selectedType == type
                            ) {
                                selectedType = type
                            }
                        }
                    }
                }
                
                // Relationship Strength
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Connection Strength")
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                        
                        Spacer()
                        
                        Text("\(Int(strengthValue * 100))%")
                            .font(DesignSystem.Typography.callout)
                            .foregroundColor(selectedType.color)
                            .fontWeight(.semibold)
                    }
                    
                    Slider(value: $strengthValue, in: 0...1, step: 0.1)
                        .accentColor(selectedType.color)
                    
                    HStack {
                        Text("Weak")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                        
                        Spacer()
                        
                        Text("Strong")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.subtleText)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .background(DesignSystem.Colors.primaryCream)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveRelationship()
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func saveRelationship() {
        relationship.type = selectedType
        relationship.strength = strengthValue
        relationship.lastUpdated = Date()
        
        spatialViewModel.updateRelationshipStrength(relationship.id, strength: strengthValue)
    }
}

// MARK: - Relationship Type Card

struct RelationshipTypeCard: View {
    let type: RelationshipType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: type.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : type.color)
                
                Text(type.displayName)
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(isSelected ? .white : DesignSystem.Colors.warmBlack)
                    .fontWeight(.medium)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? type.color : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(type.color, lineWidth: isSelected ? 0 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Extensions

extension RelationshipType {
    var color: Color {
        switch self {
        case .family: return .red.opacity(0.8)
        case .friend: return .blue.opacity(0.8)
        case .colleague: return .green.opacity(0.8)
        case .romantic: return .pink.opacity(0.8)
        case .acquaintance: return .gray.opacity(0.6)
        case .mentor: return .purple.opacity(0.8)
        case .neighbor: return .orange.opacity(0.8)
        }
    }
    
    var icon: String {
        switch self {
        case .family: return "heart.fill"
        case .friend: return "person.2.fill"
        case .colleague: return "briefcase.fill"
        case .romantic: return "heart.circle.fill"
        case .acquaintance: return "person.fill"
        case .mentor: return "graduationcap.fill"
        case .neighbor: return "house.fill"
        }
    }
}
