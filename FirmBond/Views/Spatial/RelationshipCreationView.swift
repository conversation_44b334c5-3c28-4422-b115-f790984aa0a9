//
//  RelationshipCreationView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct RelationshipCreationView: View {
    @ObservedObject var spatialViewModel: SpatialViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedFromContact: SpatialContact?
    @State private var selectedToContact: SpatialContact?
    @State private var relationshipType: RelationshipType = .friend
    @State private var relationshipStrength: Double = 0.5
    @State private var showingSuccess: Bool = false
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 24) {
                // Header
                headerSection
                
                // Contact Selection
                contactSelectionSection
                
                // Relationship Configuration
                if selectedFromContact != nil && selectedToContact != nil {
                    relationshipConfigSection
                }
                
                Spacer()
                
                // Create Button
                createButton
            }
            .padding(.horizontal, 20)
            .background(DesignSystem.Colors.primaryCream)
            .navigationTitle("Create Connection")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
        .alert("Connection Created!", isPresented: $showingSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("The relationship has been added to your memory space.")
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Image(systemName: "link.circle.fill")
                .font(.system(size: 48))
                .foregroundColor(DesignSystem.Colors.mutedGold)
            
            Text("Create Connection")
                .font(.custom("Georgia", size: 24, relativeTo: .title))
                .fontWeight(.bold)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Text("Connect two people in your memory space")
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.subtleText)
                .multilineTextAlignment(.center)
        }
        .padding(.top)
    }
    
    // MARK: - Contact Selection Section
    
    private var contactSelectionSection: some View {
        VStack(spacing: 16) {
            // From Contact
            ContactSelectionCard(
                title: "From",
                selectedContact: selectedFromContact,
                availableContacts: availableContacts
            ) { contact in
                selectedFromContact = contact
                // Clear to contact if same as from
                if selectedToContact?.id == contact?.id {
                    selectedToContact = nil
                }
            }
            
            // Connection Arrow
            if selectedFromContact != nil || selectedToContact != nil {
                Image(systemName: "arrow.down")
                    .font(.title2)
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                    .animation(.easeInOut(duration: 0.3), value: selectedFromContact != nil && selectedToContact != nil)
            }
            
            // To Contact
            ContactSelectionCard(
                title: "To",
                selectedContact: selectedToContact,
                availableContacts: availableContacts.filter { $0.id != selectedFromContact?.id }
            ) { contact in
                selectedToContact = contact
            }
        }
    }
    
    // MARK: - Relationship Configuration Section
    
    private var relationshipConfigSection: some View {
        VStack(spacing: 20) {
            Divider()
                .background(DesignSystem.Colors.glassBorder)
            
            // Relationship Type
            VStack(alignment: .leading, spacing: 12) {
                Text("Relationship Type")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    ForEach(RelationshipType.allCases, id: \.self) { type in
                        RelationshipTypeButton(
                            type: type,
                            isSelected: relationshipType == type
                        ) {
                            relationshipType = type
                        }
                    }
                }
            }
            
            // Relationship Strength
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Connection Strength")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.warmBlack)
                    
                    Spacer()
                    
                    Text("\(Int(relationshipStrength * 100))%")
                        .font(DesignSystem.Typography.callout)
                        .foregroundColor(relationshipType.color)
                        .fontWeight(.semibold)
                }
                
                Slider(value: $relationshipStrength, in: 0...1, step: 0.1)
                    .accentColor(relationshipType.color)
                
                HStack {
                    Text("Weak")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                    
                    Spacer()
                    
                    Text("Strong")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
            }
        }
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
    // MARK: - Create Button
    
    private var createButton: some View {
        Button(action: createRelationship) {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .font(.title3)
                
                Text("Create Connection")
                    .font(DesignSystem.Typography.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(canCreateRelationship ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.lightGray)
            )
        }
        .disabled(!canCreateRelationship)
        .padding(.bottom)
    }
    
    // MARK: - Computed Properties
    
    private var availableContacts: [SpatialContact] {
        return spatialViewModel.getContactsInCurrentSpace()
    }
    
    private var canCreateRelationship: Bool {
        return selectedFromContact != nil && 
               selectedToContact != nil && 
               selectedFromContact?.id != selectedToContact?.id
    }
    
    // MARK: - Actions
    
    private func createRelationship() {
        guard let fromContact = selectedFromContact,
              let toContact = selectedToContact else { return }
        
        let relationship = ContactRelationship(
            fromContactId: fromContact.id,
            toContactId: toContact.id,
            type: relationshipType,
            strength: relationshipStrength
        )
        
        spatialViewModel.addRelationship(
            relationship,
            to: spatialViewModel.currentSpaceIndex
        )
        
        // Show success and dismiss
        showingSuccess = true
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Contact Selection Card

struct ContactSelectionCard: View {
    let title: String
    let selectedContact: SpatialContact?
    let availableContacts: [SpatialContact]
    let onSelection: (SpatialContact?) -> Void
    
    @State private var showingContactPicker = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(DesignSystem.Typography.subheadline)
                .foregroundColor(DesignSystem.Colors.subtleText)
            
            Button(action: { showingContactPicker = true }) {
                HStack {
                    if let contact = selectedContact {
                        // Show selected contact
                        ContactPreviewView(contact: contact)
                    } else {
                        // Show placeholder
                        HStack {
                            Image(systemName: "person.circle.fill")
                                .font(.title2)
                                .foregroundColor(DesignSystem.Colors.lightGray)
                            
                            Text("Select Contact")
                                .font(DesignSystem.Typography.body)
                                .foregroundColor(DesignSystem.Colors.subtleText)
                            
                            Spacer()
                        }
                    }
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(DesignSystem.Colors.glassBorder, lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .sheet(isPresented: $showingContactPicker) {
            ContactPickerSheet(
                availableContacts: availableContacts,
                selectedContact: selectedContact,
                onSelection: onSelection
            )
        }
    }
}

// MARK: - Contact Preview

struct ContactPreviewView: View {
    let contact: SpatialContact
    
    var body: some View {
        HStack {
            // Contact avatar placeholder
            Circle()
                .fill(DesignSystem.Colors.mutedGold.opacity(0.2))
                .frame(width: 32, height: 32)
                .overlay(
                    Text("AB") // This would be actual initials
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                )
            
            Text("Contact Name") // This would be actual name
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.warmBlack)
            
            Spacer()
        }
    }
}

// MARK: - Contact Picker Sheet

struct ContactPickerSheet: View {
    let availableContacts: [SpatialContact]
    let selectedContact: SpatialContact?
    let onSelection: (SpatialContact?) -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            List {
                ForEach(availableContacts, id: \.id) { contact in
                    Button(action: {
                        onSelection(contact)
                        dismiss()
                    }) {
                        HStack {
                            ContactPreviewView(contact: contact)
                            
                            if selectedContact?.id == contact.id {
                                Image(systemName: "checkmark")
                                    .foregroundColor(DesignSystem.Colors.mutedGold)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Select Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Relationship Type Button

struct RelationshipTypeButton: View {
    let type: RelationshipType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Image(systemName: type.icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : type.color)
                
                Text(type.displayName)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white : DesignSystem.Colors.warmBlack)
                    .fontWeight(.medium)
            }
            .frame(height: 50)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? type.color : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(type.color, lineWidth: isSelected ? 0 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    RelationshipCreationView(
        spatialViewModel: SpatialViewModel(
            contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
            viewContext: PersistenceController.preview.container.viewContext
        )
    )
}
