//
//  AdvancedGestureHandler.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct AdvancedGestureHandler: ViewModifier {
    let contact: SpatialContact
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    
    // Gesture states
    @State private var dragOffset: CGSize = .zero
    @State private var scale: CGFloat = 1.0
    @State private var rotation: Angle = .zero
    @State private var isLongPressing: Bool = false
    @State private var showingQuickActions: Bool = false
    @State private var isCreatingRelationship: Bool = false
    @State private var relationshipDragPosition: CGPoint = .zero
    
    // Haptic feedback
    private let lightHaptic = UIImpactFeedbackGenerator(style: .light)
    private let mediumHaptic = UIImpactFeedbackGenerator(style: .medium)
    private let heavyHaptic = UIImpactFeedbackGenerator(style: .heavy)
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .rotationEffect(rotation)
            .offset(dragOffset)
            .overlay(
                quickActionsOverlay
            )
            .overlay(
                relationshipCreationOverlay
            )
            .gesture(
                simultaneousGestures
            )
    }
    
    // MARK: - Gesture Combinations
    
    private var simultaneousGestures: some Gesture {
        SimultaneousGesture(
            SimultaneousGesture(
                dragGesture,
                magnificationGesture
            ),
            SimultaneousGesture(
                rotationGesture,
                longPressGesture
            )
        )
    }
    
    // MARK: - Individual Gestures
    
    private var dragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                if !isLongPressing {
                    // Normal drag - move contact
                    dragOffset = value.translation
                    spatialViewModel.updateContactPosition(
                        contact.id,
                        position: CGPoint(
                            x: contact.position.x + value.translation.width,
                            y: contact.position.y + value.translation.height
                        )
                    )
                } else {
                    // Long press + drag - create relationship
                    isCreatingRelationship = true
                    relationshipDragPosition = CGPoint(
                        x: contact.position.x + value.translation.width,
                        y: contact.position.y + value.translation.height
                    )
                }
            }
            .onEnded { value in
                if !isLongPressing {
                    // Finalize position
                    let finalPosition = CGPoint(
                        x: contact.position.x + value.translation.width,
                        y: contact.position.y + value.translation.height
                    )
                    spatialViewModel.updateContactPosition(contact.id, position: finalPosition)
                    
                    // Reset drag offset
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        dragOffset = .zero
                    }
                    
                    lightHaptic.impactOccurred()
                } else {
                    // Check if we're over another contact to create relationship
                    handleRelationshipCreation(at: relationshipDragPosition)
                    isCreatingRelationship = false
                }
            }
    }
    
    private var magnificationGesture: some Gesture {
        MagnificationGesture()
            .onChanged { value in
                scale = value
            }
            .onEnded { value in
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    if value > 1.5 {
                        // Zoom in gesture - show contact details
                        showContactDetails()
                        scale = 1.0
                    } else if value < 0.7 {
                        // Zoom out gesture - minimize or hide
                        scale = 0.8
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            scale = 1.0
                        }
                    } else {
                        scale = 1.0
                    }
                }
                mediumHaptic.impactOccurred()
            }
    }
    
    private var rotationGesture: some Gesture {
        RotationGesture()
            .onChanged { value in
                rotation = value
            }
            .onEnded { value in
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    // Snap to nearest 90-degree angle
                    let degrees = value.degrees
                    let snappedDegrees = round(degrees / 90) * 90
                    rotation = .degrees(snappedDegrees)
                    
                    // Reset after a moment
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        rotation = .zero
                    }
                }
                lightHaptic.impactOccurred()
            }
    }
    
    private var longPressGesture: some Gesture {
        LongPressGesture(minimumDuration: 0.5)
            .onChanged { pressing in
                if pressing && !isLongPressing {
                    isLongPressing = true
                    showingQuickActions = true
                    heavyHaptic.impactOccurred()
                    
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        scale = 1.1
                    }
                }
            }
            .onEnded { _ in
                isLongPressing = false
                
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    scale = 1.0
                    showingQuickActions = false
                }
            }
    }
    
    // MARK: - Overlays
    
    private var quickActionsOverlay: some View {
        Group {
            if showingQuickActions {
                QuickActionsMenu(
                    contact: contact,
                    spatialViewModel: spatialViewModel,
                    contactsViewModel: contactsViewModel
                )
                .transition(.scale.combined(with: .opacity))
            }
        }
    }
    
    private var relationshipCreationOverlay: some View {
        Group {
            if isCreatingRelationship {
                RelationshipCreationOverlay(
                    fromContact: contact,
                    dragPosition: $relationshipDragPosition,
                    isActive: isCreatingRelationship
                )
            }
        }
    }
    
    // MARK: - Action Handlers
    
    private func showContactDetails() {
        // This will be handled by the parent view
        NotificationCenter.default.post(
            name: .showContactDetails,
            object: contact
        )
    }
    
    private func handleRelationshipCreation(at position: CGPoint) {
        // Find if there's a contact at the drop position
        guard let currentSpace = spatialViewModel.currentSpace else { return }
        
        let targetContact = currentSpace.contacts.first { targetContact in
            let distance = sqrt(
                pow(targetContact.position.x - position.x, 2) +
                pow(targetContact.position.y - position.y, 2)
            )
            return distance < 40 && targetContact.id != contact.id
        }
        
        if let targetContact = targetContact {
            // Create relationship
            let relationship = ContactRelationship(
                fromContactId: contact.id,
                toContactId: targetContact.id,
                type: .friend, // Default type
                strength: 0.5
            )
            
            spatialViewModel.addRelationship(
                relationship,
                to: spatialViewModel.currentSpaceIndex
            )
            
            // Show relationship editor
            NotificationCenter.default.post(
                name: .editRelationship,
                object: relationship
            )
            
            mediumHaptic.impactOccurred()
        }
    }
}

// MARK: - Quick Actions Menu

struct QuickActionsMenu: View {
    let contact: SpatialContact
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    
    private let actions: [QuickActionItem] = [
        QuickActionItem(icon: "phone.fill", title: "Call", color: .green),
        QuickActionItem(icon: "message.fill", title: "Message", color: .blue),
        QuickActionItem(icon: "envelope.fill", title: "Email", color: .orange),
        QuickActionItem(icon: "person.badge.plus", title: "Connect", color: .purple)
    ]
    
    var body: some View {
        HStack(spacing: 16) {
            ForEach(actions, id: \.title) { action in
                QuickActionButton(action: action) {
                    handleAction(action)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial, in: Capsule())
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .offset(y: -80) // Position above the contact
    }
    
    private func handleAction(_ action: QuickActionItem) {
        guard let person = spatialViewModel.getPerson(for: contact) else { return }
        
        switch action.title {
        case "Call":
            if let phoneNumber = person.phoneNumber,
               let url = URL(string: "tel://\(phoneNumber)") {
                UIApplication.shared.open(url)
            }
        case "Message":
            if let phoneNumber = person.phoneNumber,
               let url = URL(string: "sms://\(phoneNumber)") {
                UIApplication.shared.open(url)
            }
        case "Email":
            if let email = person.email,
               let url = URL(string: "mailto:\(email)") {
                UIApplication.shared.open(url)
            }
        case "Connect":
            // Start relationship creation mode
            NotificationCenter.default.post(
                name: .startRelationshipCreation,
                object: contact
            )
        default:
            break
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Quick Action Button

struct QuickActionButton: View {
    let action: QuickActionItem
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            Image(systemName: action.icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 40, height: 40)
                .background(action.color, in: Circle())
                .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// MARK: - Supporting Types

struct QuickActionItem {
    let icon: String
    let title: String
    let color: Color
}

// MARK: - View Modifier Extension

extension View {
    func advancedGestureHandling(
        contact: SpatialContact,
        spatialViewModel: SpatialViewModel,
        contactsViewModel: ContactsViewModel
    ) -> some View {
        self.modifier(
            AdvancedGestureHandler(
                contact: contact,
                spatialViewModel: spatialViewModel,
                contactsViewModel: contactsViewModel
            )
        )
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let showContactDetails = Notification.Name("showContactDetails")
    static let editRelationship = Notification.Name("editRelationship")
    static let startRelationshipCreation = Notification.Name("startRelationshipCreation")
}
