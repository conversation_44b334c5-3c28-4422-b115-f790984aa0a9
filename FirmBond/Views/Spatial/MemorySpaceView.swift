//
//  MemorySpaceView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct MemorySpaceView: View {
    
    // MARK: - Properties
    let space: MemorySpace
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    let isActive: Bool
    let geometry: GeometryProxy
    
    // MARK: - State
    @State private var selectedContact: SpatialContact?
    @State private var draggedContact: SpatialContact?
    @State private var dragOffset: CGSize = .zero
    @State private var showingContactDetail: Bool = false
    @State private var showingRelationshipDetail: Bool = false
    @State private var selectedRelationship: ContactRelationship?
    @State private var isCreatingRelationship: Bool = false
    @State private var relationshipCreationContact: SpatialContact?
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background
            spaceBackground

            // Relationship lines (drawn behind contacts)
            if spatialViewModel.showRelationships {
                relationshipLinesLayer
            }

            // Contacts
            contactsLayer

            // Space title overlay
            if isActive {
                spaceTitleOverlay
            }
        }
        .clipped()
        .onTapGesture(count: 2) {
            // Double tap to toggle relationship view
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                spatialViewModel.showRelationships.toggle()
            }

            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .sheet(item: $selectedContact) { contact in
            if let person = spatialViewModel.getPerson(for: contact) {
                UnifiedContactView(mode: .view(person), viewModel: contactsViewModel)
            }
        }
        .sheet(isPresented: $showingRelationshipDetail) {
            if let relationship = selectedRelationship {
                RelationshipDetailSheet(
                    relationship: .constant(relationship),
                    spatialViewModel: spatialViewModel
                )
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .showContactDetails)) { notification in
            if let contact = notification.object as? SpatialContact {
                selectedContact = contact
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .editRelationship)) { notification in
            if let relationship = notification.object as? ContactRelationship {
                selectedRelationship = relationship
                showingRelationshipDetail = true
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .startRelationshipCreation)) { notification in
            if let contact = notification.object as? SpatialContact {
                relationshipCreationContact = contact
                isCreatingRelationship = true
            }
        }
    }
    
    // MARK: - Background
    private var spaceBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    space.color.opacity(0.1),
                    space.color.opacity(0.05),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Space-specific background elements
            backgroundElements
        }
    }
    
    private var backgroundElements: some View {
        Group {
            switch space.spaceType {
            case .home:
                homeBackground
            case .work:
                workBackground
            case .social:
                socialBackground
            case .family:
                familyBackground
            case .places:
                placesBackground
            case .hobbies:
                hobbiesBackground
            }
        }
        .opacity(0.3)
    }
    
    // MARK: - Space-Specific Backgrounds
    private var homeBackground: some View {
        VStack {
            Spacer()
            HStack {
                // Couch silhouette
                RoundedRectangle(cornerRadius: 8)
                    .fill(DesignSystem.Colors.mutedGold.opacity(0.2))
                    .frame(width: 100, height: 30)
                
                Spacer()
                
                // Coffee table
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.mutedGold.opacity(0.15))
                    .frame(width: 50, height: 15)
                
                Spacer()
            }
            .padding(.bottom, 80)
        }
    }
    
    private var workBackground: some View {
        VStack {
            Spacer()
            HStack {
                // Desk
                Rectangle()
                    .fill(DesignSystem.Colors.sageGreen.opacity(0.2))
                    .frame(width: 120, height: 8)
                
                Spacer()
                
                // Chair
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.sageGreen.opacity(0.15))
                    .frame(width: 40, height: 25)
            }
            .padding(.bottom, 80)
        }
    }
    
    private var socialBackground: some View {
        // Scattered circles representing social gatherings
        ForEach(0..<5, id: \.self) { index in
            Circle()
                .fill(DesignSystem.Colors.softBlue.opacity(0.1))
                .frame(width: CGFloat.random(in: 20...40))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var familyBackground: some View {
        // Heart shapes scattered around
        ForEach(0..<3, id: \.self) { index in
            Image(systemName: "heart.fill")
                .font(.system(size: CGFloat.random(in: 20...35)))
                .foregroundColor(.red.opacity(0.1))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var placesBackground: some View {
        // Location pins
        ForEach(0..<4, id: \.self) { index in
            Image(systemName: "location.fill")
                .font(.system(size: CGFloat.random(in: 15...25)))
                .foregroundColor(.purple.opacity(0.15))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var hobbiesBackground: some View {
        // Game controller and hobby icons
        ForEach(0..<3, id: \.self) { index in
            Image(systemName: ["gamecontroller.fill", "paintbrush.fill", "music.note"].randomElement() ?? "gamecontroller.fill")
                .font(.system(size: CGFloat.random(in: 20...30)))
                .foregroundColor(.orange.opacity(0.1))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    // MARK: - Relationship Lines Layer
    private var relationshipLinesLayer: some View {
        ForEach(space.relationships, id: \.id) { relationship in
            if let fromContact = space.contact(for: relationship.fromContactId),
               let toContact = space.contact(for: relationship.toContactId) {
                RelationshipLineView(
                    fromContact: fromContact,
                    toContact: toContact,
                    relationship: relationship,
                    spatialViewModel: spatialViewModel
                ) {
                    selectedRelationship = relationship
                    showingRelationshipDetail = true
                }
            }
        }
    }

    // MARK: - Contacts Layer
    private var contactsLayer: some View {
        ForEach(space.contacts, id: \.id) { contact in
            ContactNodeView(
                contact: contact,
                spatialViewModel: spatialViewModel,
                contactsViewModel: contactsViewModel,
                isSelected: selectedContact?.id == contact.id,
                onTap: {
                    selectedContact = contact
                },
                onDragChanged: { value in
                    handleContactDragChanged(contact, value: value)
                },
                onDragEnded: { value in
                    handleContactDragEnded(contact, value: value)
                }
            )
            .advancedGestureHandling(
                contact: contact,
                spatialViewModel: spatialViewModel,
                contactsViewModel: contactsViewModel
            )
        }
    }
    
    // MARK: - Space Title Overlay
    private var spaceTitleOverlay: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: space.icon)
                            .font(.title2)
                            .foregroundColor(space.color)
                        
                        Text(space.name)
                            .font(DesignSystem.Typography.title2)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                    }
                    
                    Text("\(space.contacts.count) contacts")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Spacer()
        }
    }
    
    // MARK: - Gesture Handling
    private func handleContactDragChanged(_ contact: SpatialContact, value: DragGesture.Value) {
        draggedContact = contact
        dragOffset = value.translation

        // Don't update position during drag - just store the offset
        // The visual position will be updated by the .position modifier
    }
    
    private func handleContactDragEnded(_ contact: SpatialContact, value: DragGesture.Value) {
        let finalPosition = CGPoint(
            x: contact.position.x + value.translation.width,
            y: contact.position.y + value.translation.height
        )
        
        // Ensure position is within bounds
        let boundedPosition = boundPosition(finalPosition)
        spatialViewModel.updateContactPosition(contact.id, position: boundedPosition)
        
        // Reset drag state
        draggedContact = nil
        dragOffset = .zero
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    private func boundPosition(_ position: CGPoint) -> CGPoint {
        let margin: CGFloat = 40
        let maxX = geometry.size.width - margin
        let maxY = geometry.size.height - margin
        
        return CGPoint(
            x: max(margin, min(maxX, position.x)),
            y: max(margin, min(maxY, position.y))
        )
    }

    // MARK: - Safe Random Position Helpers
    private func safeRandomX() -> CGFloat {
        let margin: CGFloat = 50
        let width = max(100, geometry.size.width) // Ensure minimum width
        let maxX = width - margin

        guard maxX > margin else { return width / 2 }
        return CGFloat.random(in: margin...maxX)
    }

    private func safeRandomY() -> CGFloat {
        let margin: CGFloat = 100
        let height = max(200, geometry.size.height) // Ensure minimum height
        let maxY = height - margin

        guard maxY > margin else { return height / 2 }
        return CGFloat.random(in: margin...maxY)
    }
}



// MARK: - Preview
#Preview {
    GeometryReader { geometry in
        MemorySpaceView(
            space: DefaultSpacesFactory.createDefaultSpaces()[0],
            spatialViewModel: SpatialViewModel(
                contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
                viewContext: PersistenceController.preview.container.viewContext
            ),
            contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
            isActive: true,
            geometry: geometry
        )
    }
}
