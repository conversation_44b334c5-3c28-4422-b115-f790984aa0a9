//
//  ContentView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @AppStorage("useSpatialInterface") private var useSpatialInterface = false
    @StateObject private var contactsViewModel: ContactsViewModel

    init() {
        let context = PersistenceController.shared.container.viewContext
        self._contactsViewModel = StateObject(wrappedValue: ContactsViewModel(viewContext: context))
    }

    var body: some View {
        TabView {
            // Traditional List View
            ContactsListView(viewContext: viewContext)
                .tabItem {
                    Image(systemName: "list.bullet")
                    Text("List")
                }

            // New Spatial View
            SpatialNavigationView(contactsViewModel: contactsViewModel, viewContext: viewContext)
                .tabItem {
                    Image(systemName: "map")
                    Text("Spaces")
                }
        }
        .accentColor(DesignSystem.Colors.mutedGold)
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
