//
//  ContentView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @AppStorage("useSpatialInterface") private var useSpatialInterface = false
    @AppStorage("useGestureInterface") private var useGestureInterface = false
    @AppStorage("hasCompletedGestureTutorial") private var hasCompletedGestureTutorial = false
    @StateObject private var contactsViewModel: ContactsViewModel
    @State private var showingGestureTutorial = false

    init() {
        let context = PersistenceController.shared.container.viewContext
        self._contactsViewModel = StateObject(wrappedValue: ContactsViewModel(viewContext: context))
    }

    var body: some View {
        ZStack {
            if useGestureInterface {
                // Revolutionary Gesture-Only Interface
                EdgeNavigationView(contactsViewModel: contactsViewModel)
                    .onAppear {
                        // Show tutorial for first-time gesture users
                        if !hasCompletedGestureTutorial {
                            showingGestureTutorial = true
                        }
                    }
            } else {
            TabView {
                // Traditional List View
                ContactsListView(viewContext: viewContext)
                    .tabItem {
                        Image(systemName: "list.bullet")
                        Text("List")
                    }

                // Spatial View
                SpatialNavigationView(contactsViewModel: contactsViewModel, viewContext: viewContext)
                    .tabItem {
                        Image(systemName: "map")
                        Text("Spaces")
                    }

                // Gesture Interface Toggle
                SettingsView(
                    useGestureInterface: $useGestureInterface,
                    useSpatialInterface: $useSpatialInterface
                )
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
            }
            .accentColor(DesignSystem.Colors.mutedGold)
            }

            // Gesture Tutorial Overlay
            if showingGestureTutorial {
                GestureTutorialView(contactsViewModel: contactsViewModel) {
                    hasCompletedGestureTutorial = true
                    showingGestureTutorial = false
                }
                .transition(.opacity)
                .zIndex(1000)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: showingGestureTutorial)
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
